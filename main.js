import Vue from "vue";
import App from "./App";
import store from "./store"; // store
import plugins from "./plugins"; // plugins
import "./permission"; // permission
import Vant from "vant";
import "vant/lib/index.less";
import VmFormRender from "./static/VmFormRender.umd.min.js"; //引入VFormRender组件
import "./static/VmFormRender.css"; //引入VFormRender样式
import mobileIgwFunc from "./static/mobile-igw-func.umd.min.js"; // 引入 mobile-igw-func
App.mpType = "app";

// 配置对象
const config = {
  APP_INFO: "https://igw.sgcc.com.cn/connect/oauth2/authorize?", // i国网微信应用信息
  VUE_APP_OAUTH_URL: "", // 获取wx_code地址
  VUE_APP_TICKET_URL:
    "http://id.sgcc.com.cn:18088/eeee/identity/getAuthTicketByWechatCode", // 获取ISC票据地址
  VUE_APP_REDIRECT_URI: "zipapp://local.host/index.html", // 获取wx_code后重定向地址
  VUE_APP_RETRY_COUNT: 5, // 安全链接建立失败重试次数
  VUE_APP_APPNAME:
    "20211102111530914080008007111343$20180523164526158068060000170585$ANDR0ID", // 移动支撑平台的应用名
  VUE_APP_URL: "/istateinvoke-server/iStateGridInvoke", // 接口请求地址
  VUE_APP_RESOURCE: "masp", // 请求携带的资源指向
  VUE_APP_IS_LOCALHOST: 0, // 是否本地调试 1在线 0离线
  VUE_APP_FTPKEY: "", // 附件中转标识
  VUE_APP_TYPE: "prod", // 环境类型 prod生产环境 uat uat环境 dev开发环境
  VUE_APP_REQUESTSECURITY: 1, // 请求时的入参是否加密
  VUE_APP_IS_ATTESTATION: 1, // 是否要验签
};

Vue.config.productionTip = false;
Vue.prototype.$store = store;
Vue.use(plugins);
Vue.use(Vant); // 全局注册 Vant
Vue.use(VmFormRender); // 全局注册 VFormRender 等组件

// 检查 wx 对象是否就绪
function checkWxReady(maxAttempts = 10, maxRefreshAttempts = 3) {
  return new Promise((resolve, reject) => {
    let attempts = 0;
    let refreshAttempts = 0;

    const check = () => {
      if (typeof wx !== "undefined" && wx.config) {
        resolve();
      } else if (attempts >= maxAttempts) {
        if (refreshAttempts < maxRefreshAttempts) {
          console.warn(`wx 对象初始化超时，尝试刷新页面 (${refreshAttempts + 1}/${maxRefreshAttempts})`);
          refreshAttempts++;
          attempts = 0; // 重置尝试计数
          location.reload(); // 刷新页面
        } else {
          const error = new Error("wx 对象初始化超时，已达到最大刷新次数");
          console.error(error);
          reject(error);
        }
      } else {
        attempts++;
        setTimeout(check, 100);
      }
    };
    check();
  });
}

// 初始化 mobile-igw-func
checkWxReady()
  .then(() => {
    Vue.use(mobileIgwFunc, config, (userInfo) => {
      console.log("mobile-igw-func 初始化成功");

      // 回调函数，可以处理用户信息
    });
    console.log("mobile-igw-func 初始化成功");
  })
  .catch((error) => {
    console.error("无法初始化 mobile-igw-func，wx 对象加载失败:", error);
  });

// 写一个全局 this.$message
Vue.prototype.$message = (msg) => {
  // 用 vant 的 message 组件
  Vue.prototype.$toast(msg);
};
// 开发环境下启用 vconsole
const VConsole = require("vconsole");
new VConsole();

const app = new Vue({
  ...App,
});

app.$mount();
