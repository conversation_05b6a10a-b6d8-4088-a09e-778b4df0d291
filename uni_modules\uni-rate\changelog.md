## 1.3.1（2022-02-25）
- 修复 条件判断 `NaN` 错误的 bug
## 1.3.0（2021-11-19）
- 优化 组件UI，并提供设计资源，详见:[https://uniapp.dcloud.io/component/uniui/resource](https://uniapp.dcloud.io/component/uniui/resource)
- 文档迁移，详见:[https://uniapp.dcloud.io/component/uniui/uni-rate](https://uniapp.dcloud.io/component/uniui/uni-rate)
## 1.2.2（2021-09-10）
- 优化 默认值修改为 0 颗星
## 1.2.1（2021-07-30）
- 优化 vue3下事件警告的问题
## 1.2.0（2021-07-13）
- 组件兼容 vue3，如何创建vue3项目，详见 [uni-app 项目支持 vue3 介绍](https://ask.dcloud.net.cn/article/37834)
## 1.1.2（2021-05-12）
- 新增 组件示例地址
## 1.1.1（2021-04-21）
- 修复 布局变化后 uni-rate  星星计算不准确的 bug
- 优化 添加依赖 uni-icons, 导入 uni-rate 自动下载依赖
## 1.1.0（2021-04-16）
- 修复 uni-rate 属性 margin 值为 string 组件失效的 bug

## 1.0.9（2021-02-05）
- 优化 组件引用关系，通过uni_modules引用组件

## 1.0.8（2021-02-05）
- 调整为uni_modules目录规范
- 支持 pc 端
