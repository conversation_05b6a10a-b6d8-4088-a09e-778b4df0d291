## 1.2.3（2023-03-28）
- 修复 在vue3模式下可能会出现错误的问题
## 1.2.2（2022-11-29）
- 优化 主题样式
## 1.2.1（2022-06-06）
- 修复 微信小程序存在无使用组件的问题
## 1.2.0（2021-11-19）
- 优化 组件UI，并提供设计资源，详见:[https://uniapp.dcloud.io/component/uniui/resource](https://uniapp.dcloud.io/component/uniui/resource)
- 文档迁移，详见:[https://uniapp.dcloud.io/component/uniui/uni-table](https://uniapp.dcloud.io/component/uniui/uni-table)
## 1.1.0（2021-07-30）
- 组件兼容 vue3，如何创建vue3项目，详见 [uni-app 项目支持 vue3 介绍](https://ask.dcloud.net.cn/article/37834)
## 1.0.7（2021-07-08）
- 新增 uni-th 支持 date 日期筛选范围
## 1.0.6（2021-07-05）
- 新增 uni-th 支持 range 筛选范围
## 1.0.5（2021-06-28）
- 新增 uni-th 筛选功能
## 1.0.4（2021-05-12）
- 新增 示例地址
- 修复 示例项目缺少组件的Bug
## 1.0.3（2021-04-16）
- 新增 sortable 属性，是否开启单列排序
- 优化 表格多选逻辑
## 1.0.2（2021-03-22）
- uni-tr 添加 disabled 属性，用于 type=selection 时，设置某行是否可由全选按钮控制
## 1.0.1（2021-02-05）
- 调整为uni_modules目录规范
