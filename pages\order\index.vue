<template>
  <div class="w-full bg-gray-50 pb-[76px]">
    <!-- 台区信息 -->
    <div class="p-4">
      <div class="bg-white rounded-lg p-4 mb-4">
        <div class="text-base font-bold text-gray-800 mb-4">相关信息</div>
        <div v-if="orderDetail" class="text-sm">
          <!-- 台区基础信息 -->
          <div class="grid grid-cols-1 gap-2">
            <div class="flex py-2">
              <span class="w-32 text-gray-600">供电单位编码:</span>
              <span class="flex flex-1">{{ orderDetail.psNo }}</span>
            </div>
            <div class="flex py-2">
              <span class="w-32 text-gray-600">供电单位名称:</span>
              <span class="flex flex-1">{{ orderDetail.psName }}</span>
            </div>
            <div class="flex py-2">
              <span class="w-32 text-gray-600">台区编码:</span>
              <span class="flex flex-1">{{ orderDetail.tgNo }}</span>
            </div>
            <div class="flex py-2">
              <span class="w-32 text-gray-600">台区名称:</span>
              <span class="flex flex-1">{{ orderDetail.tgName }}</span>
            </div>
            <div class="flex py-2">
              <span class="w-32 text-gray-600">台区容量:</span>
              <span class="flex flex-1">{{ orderDetail.tgCap }}kVA</span>
            </div>
            <div class="flex py-2">
              <span class="w-32 text-gray-600">台区经理名称:</span>
              <span class="flex flex-1">{{ orderDetail.mngrName }}</span>
            </div>
            <div class="flex py-2">
              <span class="w-32 text-gray-600">台区经理编号:</span>
              <span class="flex flex-1">{{
                orderDetail.distStaManagerNo
              }}</span>
            </div>
            <div class="flex py-2">
              <span class="w-32 text-gray-600">线路编号:</span>
              <span class="flex flex-1">{{ orderDetail.lineNo }}</span>
            </div>
            <div class="flex py-2">
              <span class="w-32 text-gray-600">线路名称:</span>
              <span class="flex flex-1">{{ orderDetail.lineName }}</span>
            </div>
            <div class="flex py-2">
              <span class="w-32 text-gray-600">网格编号:</span>
              <span class="flex flex-1">{{ orderDetail.gridNo }}</span>
            </div>
            <div class="flex py-2">
              <span class="w-32 text-gray-600">网格名称:</span>
              <span class="flex flex-1">{{ orderDetail.gridName }}</span>
            </div>
          </div>

          <!-- 分割线 -->
          <div class="border-t border-gray-100 my-4"></div>

          <!-- 巡视统计 -->
          <div class="grid grid-cols-2 gap-4">
            <div class="bg-gray-50 rounded-lg p-3">
              <div class="text-gray-600 text-sm mb-1">日常巡视数量</div>
              <div class="text-xl font-bold text-blue-600">
                {{ orderDetail.dailyTourNum || 0 }}
              </div>
            </div>
            <div class="bg-gray-50 rounded-lg p-3">
              <div class="text-gray-600 text-sm mb-1">特殊巡视数量</div>
              <div class="text-xl font-bold text-blue-600">
                {{ orderDetail.specialTourNum || 0 }}
              </div>
            </div>
          </div>
        </div>
        <div v-else class="animate-pulse">
          <div class="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
          <div class="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>

      <!-- 待处理工单 -->
      <div class="bg-white rounded-lg p-4 mb-4">
        <div class="text-base font-bold text-gray-800 mb-4">待处理工单</div>
        <template v-if="formDataStorageList && formDataStorageList.length > 0">
          <div
            v-for="(item, index) in formDataStorageList"
            :key="index"
            class="border-b border-gray-100 last:border-0 py-2"
          >
            <div class="flex justify-between items-center">
              <div>
                <div class="flex py-1">
                  <span class="w-20 text-gray-600">类型:</span>
                  <span class="text-gray-800">{{
                    getFormTypeLabel(item.formType)
                  }}</span>
                </div>
                <div class="flex py-1">
                  <span class="w-20 text-gray-600">状态:</span>
                  <span :class="getStatusClass(item.formStatus)">
                    {{ getFormStatusLabel(item.formStatus) }}
                  </span>
                </div>
              </div>
              <button
                @click="onFormSubmit(item)"
                class="bg-blue-600 text-white text-sm px-4 py-2 rounded-full"
              >
                点击填单
              </button>
            </div>
          </div>
        </template>
        <div v-else class="text-center py-8 text-gray-500">暂无待处理工单</div>
      </div>

      <!-- 历史工单（整合后只保留这一个） -->
      <!-- <div class="bg-white rounded-lg p-4 mb-4">
        <div class="text-base font-bold text-gray-800 mb-4">历史工单</div>
        <div class="grid grid-cols-2 gap-4">
          <div
            v-for="item in historyItems"
            :key="item.type"
            @click="onClickHistory(item)"
            class="bg-gray-50 rounded-lg p-3 text-center cursor-pointer"
          >
            <div class="text-xl font-bold text-blue-600">
              {{ item.count || 0 }}
            </div>
            <div class="text-sm text-gray-600">{{ item.label }}</div>
          </div>
        </div>
      </div> -->
    </div>
  </div>
</template>

<script>
import { getHistoryInfoList, getDefaultFormByNoOrder } from "@/api";

export default {
  data() {
    return {
      orderDetail: null,
      scanData: null,
      historyItems: [
        { label: "日常巡视", type: "1", count: 0, formType: "1" },
        { label: "特殊巡视", type: "2", count: 0, formType: "2" },
      ],
      formDataStorageList: [], // 新增待处理工单列表
    };
  },
  async onLoad(options) {
    if (options.scanData) {
      try {
        const scanData = JSON.parse(decodeURIComponent(options.scanData));
        console.log("扫码数据:", scanData); // 添加日志
        this.scanData = scanData;
        this.orderDetail = scanData.data;

        // 初始化待处理工单列表
        this.formDataStorageList = this.orderDetail.formDataStorageList || [];

        // 设置历史工单数量
        if (this.orderDetail) {
          this.historyItems = [
            {
              label: "日常巡视",
              type: "1",
              count: this.orderDetail.dailyTourNum || 0,
              formType: "1",
            },
            {
              label: "特殊巡视",
              type: "2",
              count: this.orderDetail.specialTourNum || 0,
              formType: "2",
            },
          ];
        }
      } catch (error) {
        console.error("解析扫码数据失败:", error);
        uni.showToast({
          title: "获取数据失败",
          icon: "error",
        });
      }
    }
  },
  methods: {
    // 点击填单处理
    onFormSubmit(item) {
      // 修改: 直接传递formId和formDataId参数
      if (!item.formId || !item.formDataId) {
        uni.showToast({
          title: "工单数据不完整",
          icon: "none",
        });
        return;
      }

      uni.navigateTo({
        url: `/pages/form/index?formId=${item.formId}&formDataId=${item.formDataId}`,
      });
    },

    async onClickHistory(item) {
      if (!item.count) return;
      try {
        const params = {
          tgNo: this.orderDetail.tgNo,
          formType: item.formType,
        };
        const res = await getHistoryInfoList(params);
        const { data } = res;
        if (!data || !data.length) {
          uni.showToast({
            title: "暂无历史工单",
            icon: "none",
          });
          return;
        }
        uni.navigateTo({
          url: `/pages/history/index?historyData=${encodeURIComponent(
            JSON.stringify(data)
          )}`,
        });
      } catch (error) {
        // 优先使用服务器返回的msg字段
        let errorMsg = "获取历史工单失败";
        if (error.response?.msg) {
          errorMsg = error.response.msg;
        } else if (error.message) {
          errorMsg = error.message;
        }
        uni.showToast({
          title: errorMsg,
          icon: "none",
        });
      }
    },

    onBackPress() {
      uni.reLaunch({
        url: "/pages/index",
      });
      return true;
    },

    // 获取表单状态标签
    getFormStatusLabel(status) {
      const statusMap = {
        0: "超时未完成",
        1: "已创建",
        2: "进行中",
        3: "按时完成",
        4: "超时完成",
      };
      return statusMap[status] || "未知状态";
    },

    // 获取状态样式类
    getStatusClass(status) {
      const classMap = {
        0: "text-red-500",
        1: "text-blue-500",
        2: "text-green-500",
        3: "text-green-600",
        4: "text-orange-500",
      };
      return classMap[status] || "text-gray-500";
    },

    // 表单类型标签
    getFormTypeLabel(type) {
      const types = {
        1: "日常巡视",
        2: "特殊巡视",
        3: "日常走访",
        4: "特殊走访",
        5: "工单走访",
        6: "工单巡视",
        7: "默认走访",
        8: "默认巡视",
      };
      return types[type] || "未知类型";
    },
  },
};
</script>

<style lang="scss" scoped>
.grid {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

@media (min-width: 640px) {
  .grid {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}
</style>
