import store from '@/store'
import config from '@/config'
import { getToken } from '@/utils/auth'
import errorCode from '@/utils/errorCode'
import { toast, showConfirm, tansParams } from '@/utils/common'
import { URL_CODE_MAP } from '../static/urlCode'

let timeout = 10000
const baseUrl = config.baseUrl
let requestCount = 0

const request = config => {
  let userInfo = JSON.parse(sessionStorage.getItem('getUserInfo'))

  if (process.env.NODE_ENV === 'production') {
    if (config.url === '/h5/login') {
      requestCount++
      console.log(`登录请求次数: ${requestCount}`, {
        time: new Date().toLocaleTimeString(),
        config
      })
    }

    // 调试信息
    console.log('请求配置:', {
      url: config.url,
      providedUrlCode: config.urlCode,
      mappedUrlCode: URL_CODE_MAP[config.url],
      params: config.params,
      data: config.data,
      all: config
    })

    // 修改urlCode获取逻辑
    const urlCode = config.urlCode || URL_CODE_MAP[config.url]
    if (!urlCode) {
      console.error('URL映射信息:', {
        url: config.url,
        allMappings: URL_CODE_MAP,
        providedUrlCode: config.urlCode
      })
      return Promise.reject('未找到对应的 urlCode')
    }

    const setWorkFinish = {
      code: urlCode,
      type: 'notRequestBody'
    }

    const headerParams = {
      thirdId: 'd6d7623b46b843d78d1c1e8ccca38314',
      token: userInfo.authToken,
    }

    // 统一处理请求数据
    let data = {
      ...headerParams,
      ...config.data,
      requestBody: config.data
    }

    return new Promise((resolve, reject) => {
      window.Api.request(setWorkFinish, { ...data })
        .then(response => {
          let responseData = response
          if (typeof response === 'string') {
            try {
              responseData = JSON.parse(response)
            } catch (e) {
              console.error('解析响应数据失败:', e)
              reject(new Error('响应数据格式错误'))
              return
            }
          }

          if (responseData.code !== 200 && responseData.code !== 0) {
            const error = new Error(responseData.msg || '请求失败')
            error.code = responseData.code
            error.response = responseData
            toast(error.message)
            reject(error)
            return
          }

          resolve(responseData)
        })
        .catch(error => {
          if (typeof error.response === 'string') {
            try {
              const parsedError = JSON.parse(error.response)
              if (parsedError.code === 200) {
                resolve(parsedError)
                return
              }
            } catch (e) {
              console.error('解析错误响应失败:', e)
            }
          }

          toast(error.message || '请求失败')
          reject(error)
        })
    })
  }

  // 开发环境处理
  const isToken = (config.headers || {}).isToken === false
  config.header = config.header || {}
  config.header['Content-Type'] = 'application/json'
  config.header['Accept'] = 'application/json'
  if (getToken() && !isToken) {
    config.header['Authorization'] = 'Bearer ' + getToken()
  }

  // 修改参数处理逻辑
  if (config.params || config.url !== '/h5/login') {
    let params = config.params || {}
    let url = config.url + '?' + tansParams(params)
    url = url.slice(0, -1)
    config.url = url
  }

  return new Promise((resolve, reject) => {
    uni.request({
      method: config.method || 'get',
      timeout: config.timeout || timeout,
      url: config.baseUrl || baseUrl + config.url,
      data: config.data,
      header: config.header,
      dataType: 'json'
    }).then(response => {
      let [error, res] = response
      if (error) {
        toast('后端接口连接异常')
        reject(error)
        return
      }
      const code = res.data.code || 200
      const msg = errorCode[code] || res.data.msg || errorCode['default']

      if (code !== 200) {
        const err = new Error(msg)
        err.code = code
        err.response = res.data

        if (code === 401) {
          showConfirm('登录状态已过期，您可以继续留在该页面，或者重新登录?').then(res => {
            if (res.confirm) {
              store.dispatch('LogOut').then(res => {
                uni.reLaunch({ url: '/pages/login' })
              })
            }
          })
          reject(err)
        } else {
          toast(msg)
          reject(err)
        }
        return
      }

      resolve(res.data)
    })
      .catch(error => {
        let { message } = error
        if (message === 'Network Error') {
          message = '后端接口连接异常'
        } else if (message.includes('timeout')) {
          message = '系统接口请求超时'
        } else if (message.includes('Request failed with status code')) {
          message = '系统接口' + message.substr(message.length - 3) + '异常'
        }

        const err = new Error(message)
        err.code = error.code || 500
        err.response = error.response

        toast(message)
        reject(err)
      })
  })
}

export default request
