<template>
  <div class="container">
    <div class="scan-box">
      <video
        ref="video"
        class="scan-video"
        autoplay
        muted
        playsinline
        :controls="false"
        webkit-playsinline="true"
        x5-video-player-type="h5-page"
      ></video>
      <canvas ref="canvas" class="scan-canvas"></canvas>
      <div class="scan-line"></div>
      <div class="tip-text">
        <div>
          将{{ scanMode === "qr" ? "二维码" : "条形码" }}放入框内进行扫描
        </div>
        <div class="barcode-tip" v-if="scanMode === 'barcode'">
          扫描条形码时请保持水平并靠近<br />
          <span class="mode-restriction">当前模式只能扫描条形码</span>
        </div>
        <div class="qrcode-tip" v-if="scanMode === 'qr'">
          <span class="mode-restriction">当前模式只能扫描二维码</span>
        </div>
      </div>
    </div>
    <div class="scan-border"></div>
    <div v-if="showSuccessEffect" class="success-effect">
      <div class="success-icon">
        <uni-icons type="checkmarkempty" size="60" color="#fff"></uni-icons>
      </div>
      <div class="success-text">扫码成功</div>
    </div>
    <view class="camera-toggle-btn" @click="toggleCamera">
      <text>切换摄像头</text>
    </view>
    <view class="flashlight-btn" @click="toggleFlashlight">
      <uni-icons
        :type="flashlightOn ? 'fire-filled' : 'fire'"
        size="24"
        color="#fff"
      ></uni-icons>
      <text>{{ flashlightOn ? "关闭闪光灯" : "开启闪光灯" }}</text>
    </view>

    <view class="scan-mode-container">
      <view
        class="scan-mode-btn"
        :class="{ active: scanMode === 'qr' }"
        @click="switchScanMode('qr')"
      >
        <text>扫二维码</text>
      </view>
      <view
        class="scan-mode-btn"
        :class="{ active: scanMode === 'barcode' }"
        @click="switchScanMode('barcode')"
      >
        <text>扫条形码</text>
      </view>
    </view>
  </div>
</template>

<script>
import Quagga from "quagga";
// 对于二维码扫描，我们需要添加qr-scanner库
// import QrScanner from "qr-scanner";
import { getQrcode } from "@/api";

export default {
  data() {
    return {
      isScanning: false,
      showSuccessEffect: false,
      lastScanTime: 0,
      isProcessing: false,
      isDestroyed: false,
      videoElement: null,
      scanType: "0", // 默认为巡视扫码类型
      scanInterval: null,
      initRetryCount: 0,
      maxRetryCount: 3,
      flashlightOn: false,
      scanMode: "qr", // 扫描模式：'qr'(二维码), 'barcode'(条形码)，默认为二维码
      qrScanner: null, // QR Scanner实例
      currentStream: null, // 当前视频流
      cameraIndex: 0, // 当前摄像头索引
      availableCameras: [], // 可用摄像头列表
    };
  },
  onLoad(options) {
    // 获取扫码类型参数
    if (options && options.type) {
      this.scanType = options.type;

      // 根据扫码类型设置默认扫码模式
      if (this.scanType === "1") {
        // 走访扫码，默认选中条形码
        this.scanMode = "barcode";
        console.log("[QRCode] onLoad: 走访扫码，默认设置为条形码模式");
      } else {
        // 巡视扫码，默认选中二维码
        this.scanMode = "qr";
        console.log("[QRCode] onLoad: 巡视扫码，默认设置为二维码模式");
      }

      console.log(
        "[QRCode] onLoad: 扫码类型设置为",
        this.scanType,
        "扫码模式设置为",
        this.scanMode
      );
    }
  },
  mounted() {
    console.log("[QRCode] mounted");
    this.isScanning = false;
    this.isProcessing = false;
    this.isDestroyed = false;

    setTimeout(() => {
      this.initScanner();
    }, 500);
  },

  async onShow() {
    console.log("[QRCode] onShow");

    if (this.isDestroyed) {
      this.isDestroyed = false;
      this.isScanning = false;
      this.isProcessing = false;
    }

    // 强制释放可能残留的摄像头资源
    await this.forceReleaseAllCameraResources();

    if (!this.isScanning && !this.isDestroyed && !this.isProcessing) {
      // 延长等待时间，确保其他页面的摄像头资源完全释放
      setTimeout(() => {
        this.initScanner();
      }, 2000); // 增加到2秒
    }
  },
  onHide() {
    console.log("[QRCode] onHide called");

    // 强制停止扫描和释放资源，不管是否在处理中
    this.forceStopAndRelease();
  },
  onUnload() {
    console.log("[QRCode] onUnload called");

    this.isDestroyed = true;

    if (this.scanInterval) {
      clearInterval(this.scanInterval);
      this.scanInterval = null;
    }

    // 强制停止扫描和释放资源
    this.forceStopAndRelease();

    this.isScanning = false;
    this.isProcessing = false;
  },

  methods: {
    // 强制停止扫描并释放所有资源
    forceStopAndRelease() {
      console.log("[QRCode] forceStopAndRelease called");

      // 立即设置标志位
      this.isScanning = false;

      try {
        // 强制释放视频资源
        this.releaseVideoResources();

        // 停止不同类型的扫描器
        if (this.scanMode === "qr" && this.qrScanner) {
          try {
            // 暂时使用通用方法停止
            if (this.qrScanner.stop) {
              this.qrScanner.stop();
            }
            this.qrScanner = null;
            console.log("[QRCode] 强制停止QR Scanner");
          } catch (error) {
            console.error("[QRCode] 强制停止QR Scanner失败:", error);
          }
        } else if (this.scanMode === "barcode") {
          try {
            Quagga.stop();
            Quagga.offDetected();
            console.log("[QRCode] 强制停止Quagga扫描器");
          } catch (error) {
            console.error("[QRCode] 强制停止Quagga扫描器失败:", error);
          }
        }

        // 清空视频元素引用
        this.videoElement = null;

        // 重置设备相关状态（对于QR Scanner）
        this.currentStream = null;
        console.log("[QRCode] 已重置设备状态");

        // 延迟重置处理状态，给资源释放一些时间
        setTimeout(() => {
          this.isProcessing = false;
          console.log("[QRCode] 延迟重置处理状态完成");
        }, 1000);
      } catch (error) {
        console.error("[QRCode] 强制释放资源时发生错误:", error);
      }
    },
    // 初始化扫描器
    async initScanner() {
      console.log("[QRCode] initScanner called, mode:", this.scanMode);

      if (this.isScanning || this.isDestroyed || this.isProcessing) {
        console.log(
          "[QRCode] 扫描器已初始化或组件已销毁或正在处理中，取消初始化"
        );
        return false;
      }

      try {
        // 等待摄像头资源完全释放
        await this.waitForCameraRelease();

        // 获取视频元素，添加重试机制
        let retryCount = 0;
        let videoElement = null;

        while (retryCount < 3 && !videoElement) {
          videoElement = this.getVideoElement();
          if (!videoElement) {
            console.log(
              `[QRCode] 第${retryCount + 1}次获取视频元素失败，等待重试...`
            );
            await new Promise((resolve) => setTimeout(resolve, 500));
            retryCount++;
          }
        }

        if (!videoElement) {
          throw new Error("无法获取视频元素，请检查页面是否正确渲染");
        }

        this.videoElement = videoElement;
        console.log("[QRCode] 成功获取视频元素:", this.videoElement);

        // 停止之前的扫描器
        this.stopScanner();

        if (this.scanMode === "qr") {
          // 使用 QR Scanner 处理二维码
          console.log("[QRCode] 初始化QR Scanner，优先使用后置摄像头");

          // 尝试获取具体的后置摄像头设备ID
          const backCameraDeviceId = await this.getBackCameraDeviceId();

          const qrScannerOptions = {
            returnDetailedScanResult: true,
            maxScansPerSecond: 5,
          };

          // 使用三层保障机制确保后置摄像头优先选择
          if (backCameraDeviceId) {
            qrScannerOptions.preferredCamera = backCameraDeviceId;
            console.log(
              "[QRCode] ZXing第一层：使用指定的后置摄像头设备ID:",
              backCameraDeviceId
            );
          } else {
            qrScannerOptions.preferredCamera = "environment";
            console.log("[QRCode] ZXing第二层：使用facingMode environment");
          }

          // 暂时禁用QrScanner，使用ZXing代替
          // this.qrScanner = new QrScanner(
          //   this.videoElement,
          //   (result) => {
          //     if (this.validateScanResult(result.data)) {
          //       this.handleScanResult(result.data);
          //     }
          //   },
          //   qrScannerOptions
          // );

          // 使用ZXing处理二维码，添加重试机制
          const { BrowserMultiFormatReader } = require("@zxing/browser");
          const { BarcodeFormat, DecodeHintType } = require("@zxing/library");

          this.qrScanner = new BrowserMultiFormatReader();
          const hints = new Map();
          hints.set(DecodeHintType.POSSIBLE_FORMATS, [
            BarcodeFormat.QR_CODE,
            BarcodeFormat.DATA_MATRIX,
            BarcodeFormat.AZTEC,
            BarcodeFormat.PDF_417,
          ]);
          hints.set(DecodeHintType.ALSO_INVERTED, true);
          hints.set(DecodeHintType.TRY_HARDER, true);
          this.qrScanner.hints = hints;

          // 添加ZXing初始化重试机制
          let zxingInitSuccess = false;
          let zxingRetryCount = 0;
          const zxingMaxRetries = 3;

          while (!zxingInitSuccess && zxingRetryCount < zxingMaxRetries) {
            try {
              // 构建约束条件
              let constraints = {
                video: {
                  width: { min: 1280 },
                  height: { min: 720 },
                },
              };

              // 根据重试次数调整约束
              if (zxingRetryCount === 0 && backCameraDeviceId) {
                // 第一次：设备ID + facingMode双重约束
                constraints.video.facingMode = "environment";
                constraints.video.deviceId = { exact: backCameraDeviceId };
                console.log("[QRCode] ZXing第一次尝试：设备ID + facingMode");
              } else if (zxingRetryCount === 1) {
                // 第二次：仅facingMode约束
                constraints.video.facingMode = "environment";
                console.log("[QRCode] ZXing第二次尝试：仅facingMode");
              } else {
                // 第三次：兼容性配置
                constraints.video = {
                  width: { min: 640 },
                  height: { min: 480 },
                };
                console.log("[QRCode] ZXing第三次尝试：兼容性配置");
              }

              // 使用decodeFromVideoDevice方法
              await this.qrScanner.decodeFromVideoDevice(
                undefined, // 使用默认设备或约束中指定的设备
                this.videoElement,
                (result) => {
                  if (this.validateScanResult(result.text)) {
                    this.handleScanResult(result.text);
                  }
                }
              );

              zxingInitSuccess = true;
              console.log(
                `[QRCode] ZXing初始化成功 (重试${
                  zxingRetryCount + 1
                }/${zxingMaxRetries})`
              );
            } catch (error) {
              zxingRetryCount++;
              console.error(
                `[QRCode] ZXing初始化失败 (重试${zxingRetryCount}/${zxingMaxRetries}):`,
                error
              );

              if (zxingRetryCount >= zxingMaxRetries) {
                throw error;
              }

              console.log(
                `[QRCode] ZXing初始化重试 ${zxingRetryCount}/${zxingMaxRetries}`
              );
              await new Promise((resolve) => setTimeout(resolve, 1000));
            }
          }

          // 暂时注释掉start方法，需要用decodeFromVideoDevice
          // await this.qrScanner.start();
          // 获取视频流用于闪光灯控制
          if (this.videoElement && this.videoElement.srcObject) {
            this.currentStream = this.videoElement.srcObject;
            console.log("[QRCode] 已获取QR视频流:", this.currentStream);

            // 检查闪光灯支持
            const videoTracks = this.currentStream.getVideoTracks();
            if (videoTracks.length > 0) {
              const track = videoTracks[0];
              const capabilities = track.getCapabilities();
              console.log("[QRCode] QR模式摄像头支持的功能:", capabilities);
              if (capabilities && capabilities.torch) {
                console.log("[QRCode] QR模式设备支持闪光灯功能");
              } else {
                console.log("[QRCode] QR模式设备不支持闪光灯功能");
              }
            }
          }
          this.isScanning = true;
          console.log("[QRCode] QR Scanner初始化成功");
        } else {
          // 使用 QuaggaJS 处理条形码
          console.log("[QRCode] 初始化Quagga扫描器，优先使用后置摄像头");

          // 尝试获取具体的后置摄像头设备ID
          const backCameraDeviceId = await this.getBackCameraDeviceId();

          const constraints = {
            width: { min: 1280 },
            height: { min: 720 },
          };

          // 使用三层保障机制确保后置摄像头优先选择
          if (backCameraDeviceId) {
            // 第一层：强制facingMode + 设备ID双重约束
            constraints.facingMode = "environment"; // 强制要求，不是ideal
            constraints.deviceId = { exact: backCameraDeviceId };
            console.log(
              "[QRCode] Quagga第一层：强制facingMode + 设备ID:",
              backCameraDeviceId
            );
          } else {
            // 第二层：仅强制facingMode约束
            constraints.facingMode = "environment"; // 强制要求
            console.log("[QRCode] Quagga第二层：强制facingMode environment");
          }

          const config = {
            inputStream: {
              name: "Live",
              type: "LiveStream",
              target: this.videoElement,
              constraints: constraints,
            },
            decoder: {
              readers: [
                "code_128_reader",
                "ean_reader",
                "ean_8_reader",
                "code_39_reader",
                "code_39_vin_reader",
                "codabar_reader",
                "upc_reader",
                "upc_e_reader",
                "i2of5_reader",
              ],
            },
            locate: true,
            frequency: 10,
          };

          // 初始化Quagga，添加重试机制
          let quaggaInitSuccess = false;
          let retryCount = 0;
          const maxRetries = 3;

          while (!quaggaInitSuccess && retryCount < maxRetries) {
            try {
              await new Promise((resolve, reject) => {
                // 如果是重试，尝试降级约束
                if (retryCount > 0) {
                  if (retryCount === 1 && backCameraDeviceId) {
                    // 第二次重试：移除设备ID约束，只保留facingMode
                    delete config.inputStream.constraints.deviceId;
                    config.inputStream.constraints.facingMode = "environment";
                    console.log("[QRCode] Quagga第二次重试：移除设备ID约束");
                  } else if (retryCount === 2) {
                    // 第三次重试：使用兼容性最好的配置
                    config.inputStream.constraints = {
                      width: { min: 640 },
                      height: { min: 480 },
                    };
                    console.log("[QRCode] Quagga第三次重试：使用兼容性配置");
                  }
                }

                Quagga.init(config, (err) => {
                  if (err) {
                    console.error(
                      `[QRCode] Quagga初始化失败 (重试${
                        retryCount + 1
                      }/${maxRetries}):`,
                      err
                    );
                    reject(err);
                  } else {
                    console.log(
                      `[QRCode] Quagga初始化成功 (重试${
                        retryCount + 1
                      }/${maxRetries})`
                    );
                    resolve();
                  }
                });
              });

              quaggaInitSuccess = true;
            } catch (error) {
              retryCount++;
              if (retryCount >= maxRetries) {
                throw error;
              }
              console.log(
                `[QRCode] Quagga初始化重试 ${retryCount}/${maxRetries}`
              );
              await new Promise((resolve) => setTimeout(resolve, 1000));
            }
          }

          // 注册扫码结果回调
          Quagga.onDetected((result) => {
            const code = result.codeResult.code;
            if (this.validateScanResult(code)) {
              this.handleScanResult(code);
            }
          });

          // 开始扫描
          Quagga.start();
          // 获取视频流用于闪光灯控制
          if (this.videoElement && this.videoElement.srcObject) {
            this.currentStream = this.videoElement.srcObject;
            console.log("[QRCode] 已获取Quagga视频流:", this.currentStream);

            // 检查闪光灯支持
            const videoTracks = this.currentStream.getVideoTracks();
            if (videoTracks.length > 0) {
              const track = videoTracks[0];
              const capabilities = track.getCapabilities();
              console.log("[QRCode] Quagga模式摄像头支持的功能:", capabilities);
              if (capabilities && capabilities.torch) {
                console.log("[QRCode] Quagga模式设备支持闪光灯功能");
              } else {
                console.log("[QRCode] Quagga模式设备不支持闪光灯功能");
              }
            }
          }
          this.isScanning = true;
          console.log("[QRCode] Quagga扫描器初始化成功");
        }

        return true;
      } catch (error) {
        console.error("[QRCode] 扫描器初始化失败:", error);
        this.stopScanner();

        // 根据错误类型显示不同的提示
        let errorMessage = "扫描器初始化失败，请重试";
        let showRetryButton = true;

        if (error.message && error.message.includes("getUserMedia")) {
          errorMessage = "无法访问摄像头，请检查权限设置";
        } else if (error.message && error.message.includes("camera")) {
          errorMessage = "未找到可用摄像头，请检查设备";
        } else if (error.message && error.message.includes("NotFoundError")) {
          errorMessage = "未找到指定摄像头，正在尝试其他摄像头";
          showRetryButton = false;
          // 自动重试使用其他摄像头
          setTimeout(() => {
            this.cameraIndex = 0; // 重置为第一个摄像头
            this.initScanner();
          }, 2000);
        } else if (error.message && error.message.includes("NotAllowedError")) {
          errorMessage = "摄像头权限被拒绝，请在设置中允许摄像头权限";
        } else if (
          error.message &&
          error.message.includes("NotReadableError")
        ) {
          errorMessage = "摄像头被其他应用占用，请关闭其他应用后重试";
        } else if (
          error.message &&
          error.message.includes("OverconstrainedError")
        ) {
          errorMessage = "摄像头不支持当前配置，正在尝试兼容模式";
          showRetryButton = false;
          // 自动重试使用兼容配置
          setTimeout(() => {
            this.initScanner();
          }, 2000);
        }

        uni.showModal({
          title: "摄像头初始化失败",
          content: errorMessage,
          showCancel: showRetryButton,
          cancelText: "返回",
          confirmText: showRetryButton ? "重试" : "确定",
          success: (res) => {
            if (res.confirm && showRetryButton) {
              // 用户选择重试
              setTimeout(() => {
                this.initScanner();
              }, 500);
            } else if (res.cancel || !showRetryButton) {
              // 用户选择返回或自动处理
              if (showRetryButton) {
                uni.navigateBack();
              }
            }
          },
        });

        return false;
      }
    },

    // 处理扫描结果
    async handleScanResult(code) {
      console.log("[QRCode] 扫描到内容:", code, "模式:", this.scanMode);

      // 防止重复处理
      const currentTime = Date.now();
      if (this.isProcessing || currentTime - this.lastScanTime < 2000) {
        return;
      }

      // 验证扫描结果是否符合当前模式
      if (!this.validateScanResult(code)) {
        const modeText = this.scanMode === "barcode" ? "条形码" : "二维码";
        uni.showToast({
          title: `请扫描${modeText}`,
          icon: "none",
          duration: 1500,
        });
        return;
      }

      this.lastScanTime = currentTime;
      this.isProcessing = true;

      try {
        // 调用API验证扫码结果
        // 根据扫码类型设置不同的参数名称
        const apiParams = {};
        if (this.scanType === "0") {
          // 巡视扫码 - 台区二维码
          apiParams.tgQrcode = code; // 使用台区二维码专用参数名
          apiParams.type = this.scanType;
        } else {
          // 其他类型扫码使用通用参数名
          apiParams.qrcode = code;
          apiParams.type = this.scanType;
        }

        console.log("[QRCode] API调用参数:", apiParams);

        const response = await getQrcode(apiParams);

        // 扫码成功（如果执行到这里，说明response.code一定是200）
        // 显示成功效果
        this.showSuccessEffect = true;
        setTimeout(() => {
          this.showSuccessEffect = false;
        }, 1500);

        uni.navigateBack({
          delta: 1,
        });

        // 传递扫码结果
        const eventName =
          this.scanMode === "barcode" ? "barcodeScanned" : "qrcodeScanned";
        uni.$emit(eventName, {
          code: code,
          type: this.scanMode,
          data: response.data,
        });
      } catch (error) {
        console.error("[QRCode] 处理扫码结果失败:", error);

        // 确保成功效果被重置
        this.showSuccessEffect = false;

        // request.js已经自动toast了错误信息，这里只需要重置处理状态
      } finally {
        // 延迟重置处理状态，避免快速重复扫描
        setTimeout(() => {
          this.isProcessing = false;
        }, 1000);
      }
    },

    // 验证扫描结果是否符合当前模式
    validateScanResult(text) {
      if (!text || text.length < 1) {
        return false;
      }

      if (this.scanMode === "barcode") {
        // 条形码验证
        // EAN-13: 13位数字
        if (/^\d{13}$/.test(text)) return true;
        // EAN-8: 8位数字
        if (/^\d{8}$/.test(text)) return true;
        // UPC-A: 12位数字
        if (/^\d{12}$/.test(text)) return true;
        // UPC-E: 6-8位数字
        if (/^\d{6,8}$/.test(text)) return true;
        // Code 128, Code 39, Code 93: 可包含字母数字
        if (/^[A-Z0-9\-\.\$\/\+\%\s]+$/i.test(text) && text.length <= 43)
          return true;

        return false;
      } else {
        // 二维码验证（相对宽松）
        // URL格式
        if (/^https?:\/\//.test(text)) return true;
        // 包含域名
        if (/\.(com|cn|org|net|gov|edu)/.test(text)) return true;
        // JSON格式
        if (text.startsWith("{") && text.endsWith("}")) {
          try {
            JSON.parse(text);
            return true;
          } catch (e) {
            // 继续其他验证
          }
        }
        // 包含特殊字符或较长内容
        if (text.length > 20 || /[{}:;,\[\]"']/.test(text)) return true;
        // 纯数字但长度超过条形码范围
        if (/^\d+$/.test(text) && text.length > 15) return true;
        // 包含中文字符
        if (/[\u4e00-\u9fa5]/.test(text)) return true;

        return true; // 对于二维码，我们相对宽松一些
      }
    },

    // 切换扫描模式
    async switchScanMode(mode) {
      console.log("[QRCode] switchScanMode:", mode);

      if (this.scanMode === mode) {
        return;
      }

      uni.showLoading({
        title: "切换中...",
        mask: true,
      });

      // 停止当前扫描
      this.stopScanner();
      this.releaseVideoResources();

      // 重置状态
      this.isScanning = false;

      // 设置新的扫描模式
      this.scanMode = mode;

      // 添加延迟确保资源完全释放
      await new Promise((resolve) => setTimeout(resolve, 500));

      try {
        // 重新初始化扫描器
        await this.initScanner();

        // 显示当前模式提示
        uni.showToast({
          title: mode === "barcode" ? "条形码扫描模式" : "二维码扫描模式",
          icon: "none",
          duration: 2000,
        });
      } catch (error) {
        console.error("[QRCode] 切换扫描模式失败:", error);
        uni.showToast({
          title: "切换失败，请重试",
          icon: "none",
          duration: 1500,
        });
      } finally {
        uni.hideLoading();
      }
    },

    // 获取视频元素
    getVideoElement() {
      try {
        // 在 uni-app 中，需要确保 DOM 已经渲染完成
        const video = this.$refs.video;
        console.log("[QRCode] 获取视频元素引用:", video);

        if (!video) {
          console.error("[QRCode] 无法获取视频元素引用");
          return null;
        }

        // 在 uni-app 中，需要获取 uni-video 内部的原生 video 元素
        if (video.$el) {
          const uniVideoEl = video.$el;
          console.log("[QRCode] uni-video 元素:", uniVideoEl);

          // 查找内部的原生 video 元素
          const nativeVideo = uniVideoEl.querySelector("video");
          if (nativeVideo) {
            console.log("[QRCode] 找到原生 video 元素:", nativeVideo);
            return nativeVideo;
          }

          // 如果没有找到 video 元素，尝试查找其他可能的视频元素
          const videoElements = uniVideoEl.querySelectorAll(
            'video, [tag="video"]'
          );
          if (videoElements.length > 0) {
            console.log("[QRCode] 找到视频元素:", videoElements[0]);
            return videoElements[0];
          }

          console.log("[QRCode] 未找到原生 video 元素，返回 uni-video 元素");
          return uniVideoEl;
        }

        return video;
      } catch (error) {
        console.error("[QRCode] 获取视频元素时发生错误:", error);
        return null;
      }
    },

    // 切换摄像头
    async toggleCamera() {
      try {
        console.log("[QRCode] 开始切换摄像头");

        // 获取当前可用的摄像头设备列表
        const devices = await navigator.mediaDevices.enumerateDevices();
        const videoDevices = devices.filter(
          (device) => device.kind === "videoinput"
        );

        if (videoDevices.length <= 1) {
          uni.showToast({
            title: "没有其他可用摄像头",
            icon: "none",
            duration: 1500,
          });
          return;
        }

        // 更新可用摄像头列表
        this.availableCameras = videoDevices;

        console.log("[QRCode] 当前摄像头索引:", this.cameraIndex);
        console.log(
          "[QRCode] 可用摄像头设备:",
          videoDevices.map((d) => ({
            deviceId: d.deviceId,
            label: d.label,
          }))
        );

        // 停止当前扫描
        this.stopScanner();
        this.releaseVideoResources();

        // 切换到下一个摄像头
        this.cameraIndex = (this.cameraIndex + 1) % videoDevices.length;
        const selectedCamera = videoDevices[this.cameraIndex];

        console.log("[QRCode] 切换到摄像头:", {
          index: this.cameraIndex,
          label: selectedCamera.label,
          deviceId: selectedCamera.deviceId,
        });

        // 添加延迟确保资源完全释放
        await new Promise((resolve) => setTimeout(resolve, 500));

        // 重新初始化扫描器
        await this.initScanner();

        uni.showToast({
          title: `已切换到: ${selectedCamera.label || "摄像头"}`,
          icon: "none",
          duration: 1500,
        });
      } catch (error) {
        console.error("[QRCode] 切换摄像头失败:", error);
        uni.showToast({
          title: "切换摄像头失败",
          icon: "none",
          duration: 1500,
        });

        // 尝试重新初始化
        setTimeout(() => {
          this.initScanner();
        }, 1000);
      }
    },

    // 控制闪光灯
    async toggleFlashlight() {
      try {
        console.log("[QRCode] 尝试控制闪光灯，当前状态:", this.flashlightOn);
        console.log("[QRCode] scanMode:", this.scanMode);
        console.log("[QRCode] qrScanner:", this.qrScanner);
        console.log("[QRCode] currentStream:", this.currentStream);

        // 方法1：尝试使用ZXing的switchTorch方法（适用于二维码模式）
        if (
          this.scanMode === "qr" &&
          this.qrScanner &&
          typeof this.qrScanner.switchTorch === "function"
        ) {
          try {
            await this.qrScanner.switchTorch();
            this.flashlightOn = !this.flashlightOn;
            console.log("[QRCode] 使用ZXing switchTorch成功");

            uni.showToast({
              title: this.flashlightOn ? "闪光灯已开启" : "闪光灯已关闭",
              icon: "none",
              duration: 1000,
            });
            return;
          } catch (error) {
            console.warn("[QRCode] ZXing switchTorch失败:", error);
          }
        }

        // 方法2：尝试使用MediaStream API控制闪光灯
        if (this.currentStream) {
          try {
            const videoTracks = this.currentStream.getVideoTracks();
            console.log("[QRCode] 视频轨道数量:", videoTracks.length);

            if (videoTracks.length > 0) {
              const track = videoTracks[0];
              const capabilities = track.getCapabilities();
              console.log("[QRCode] 摄像头能力:", capabilities);

              if (capabilities && capabilities.torch) {
                await track.applyConstraints({
                  advanced: [{ torch: !this.flashlightOn }],
                });
                this.flashlightOn = !this.flashlightOn;
                console.log("[QRCode] 使用MediaStream API控制闪光灯成功");

                uni.showToast({
                  title: this.flashlightOn ? "闪光灯已开启" : "闪光灯已关闭",
                  icon: "none",
                  duration: 1000,
                });
                return;
              } else {
                console.log("[QRCode] 设备不支持torch功能");
              }
            }
          } catch (error) {
            console.warn("[QRCode] MediaStream API控制闪光灯失败:", error);
          }
        }

        // 方法3：尝试从视频元素获取流并控制闪光灯
        if (this.videoElement && this.videoElement.srcObject) {
          try {
            const stream = this.videoElement.srcObject;
            const videoTracks = stream.getVideoTracks();
            console.log(
              "[QRCode] 从视频元素获取的轨道数量:",
              videoTracks.length
            );

            if (videoTracks.length > 0) {
              const track = videoTracks[0];
              const capabilities = track.getCapabilities();
              console.log("[QRCode] 视频元素轨道能力:", capabilities);

              if (capabilities && capabilities.torch) {
                await track.applyConstraints({
                  advanced: [{ torch: !this.flashlightOn }],
                });
                this.flashlightOn = !this.flashlightOn;
                console.log("[QRCode] 使用视频元素流控制闪光灯成功");

                uni.showToast({
                  title: this.flashlightOn ? "闪光灯已开启" : "闪光灯已关闭",
                  icon: "none",
                  duration: 1000,
                });
                return;
              }
            }
          } catch (error) {
            console.warn("[QRCode] 视频元素流控制闪光灯失败:", error);
          }
        }

        // 所有方法都失败
        console.log("[QRCode] 所有闪光灯控制方法都失败");
        uni.showToast({
          title: "当前设备不支持闪光灯",
          icon: "none",
          duration: 1500,
        });
      } catch (error) {
        console.error("[QRCode] 控制闪光灯失败:", error);
        uni.showToast({
          title: "闪光灯控制失败",
          icon: "none",
          duration: 1500,
        });
      }
    },

    // 停止扫描器
    stopScanner() {
      console.log("[QRCode] stopScanner called");

      // 先释放视频资源
      this.releaseVideoResources();

      try {
        if (this.scanMode === "qr" && this.qrScanner) {
          // 暂时使用通用方法停止
          if (this.qrScanner.stop) {
            this.qrScanner.stop();
          }
          this.qrScanner = null;
          console.log("[QRCode] QR Scanner已停止");
        } else if (this.isScanning) {
          Quagga.stop();
          Quagga.offDetected();
          console.log("[QRCode] Quagga扫描器已停止");
        }
      } catch (error) {
        console.error("[QRCode] 停止扫描器失败:", error);
      }

      this.isScanning = false;
    },

    // 释放视频资源
    releaseVideoResources() {
      console.log("[QRCode] releaseVideoResources called");

      // 释放当前视频流
      if (this.currentStream) {
        try {
          this.currentStream.getTracks().forEach((track) => {
            console.log("[QRCode] 停止视频轨道:", track.label, track.kind);
            track.stop();
          });
          this.currentStream = null;
          console.log("[QRCode] 视频流已释放");
        } catch (error) {
          console.error("[QRCode] 释放视频流失败:", error);
        }
      }

      // 从视频元素获取并释放视频流
      if (this.videoElement && this.videoElement.srcObject) {
        try {
          const stream = this.videoElement.srcObject;
          if (stream && stream.getTracks) {
            stream.getTracks().forEach((track) => {
              console.log(
                "[QRCode] 停止视频元素轨道:",
                track.label,
                track.kind
              );
              track.stop();
            });
          }
          this.videoElement.srcObject = null;
          console.log("[QRCode] 视频元素已清理");
        } catch (error) {
          console.error("[QRCode] 清理视频元素失败:", error);
        }
      }

      // 强制垃圾回收（如果支持）
      if (window.gc && typeof window.gc === "function") {
        try {
          window.gc();
          console.log("[QRCode] 已触发垃圾回收");
        } catch (error) {
          console.log("[QRCode] 垃圾回收不可用");
        }
      }
    },

    // 等待摄像头资源释放
    async waitForCameraRelease() {
      console.log("[QRCode] 等待摄像头资源释放...");

      // 检查是否有活跃的媒体流
      let retryCount = 0;
      const maxRetries = 5;

      while (retryCount < maxRetries) {
        try {
          // 尝试获取设备列表来检测摄像头状态
          const devices = await navigator.mediaDevices.enumerateDevices();
          const videoDevices = devices.filter(
            (device) => device.kind === "videoinput"
          );

          console.log("[QRCode] 检测到摄像头设备数量:", videoDevices.length);

          // 如果能正常获取设备列表，说明摄像头资源可用
          if (videoDevices.length > 0) {
            console.log("[QRCode] 摄像头资源检测正常");
            break;
          }
        } catch (error) {
          console.warn("[QRCode] 摄像头资源检测失败:", error);
        }

        retryCount++;
        console.log(
          `[QRCode] 等待摄像头资源释放，重试 ${retryCount}/${maxRetries}`
        );
        await new Promise((resolve) => setTimeout(resolve, 500));
      }
    },

    // 获取后置摄像头设备ID
    async getBackCameraDeviceId() {
      try {
        const devices = await navigator.mediaDevices.enumerateDevices();
        const videoDevices = devices.filter(
          (device) => device.kind === "videoinput"
        );

        // 更新可用摄像头列表
        this.availableCameras = videoDevices;

        console.log(
          "[QRCode] 可用摄像头设备:",
          videoDevices.map((d) => ({
            deviceId: d.deviceId,
            label: d.label,
          }))
        );

        if (videoDevices.length === 0) {
          console.log("[QRCode] 没有找到摄像头设备");
          return null;
        }

        // 如果已经有摄像头索引且在有效范围内，使用当前索引的摄像头
        if (this.cameraIndex >= 0 && this.cameraIndex < videoDevices.length) {
          const selectedCamera = videoDevices[this.cameraIndex];
          console.log("[QRCode] 使用当前索引的摄像头:", {
            index: this.cameraIndex,
            label: selectedCamera.label,
            deviceId: selectedCamera.deviceId,
          });
          return selectedCamera.deviceId;
        }

        // 优先级1：查找明确标识为后置摄像头的设备（多语言支持）
        let backCamera = videoDevices.find((device) => {
          const label = device.label.toLowerCase();
          return (
            // 英文标签
            label.includes("back") ||
            label.includes("rear") ||
            label.includes("environment") ||
            label.includes("facing back") ||
            label.includes("world") ||
            label.includes("main") ||
            label.includes("primary") ||
            label.includes("wide") ||
            label.includes("camera 0") ||
            label.includes("camera0") ||
            // 中文标签
            label.includes("后置") ||
            label.includes("外置") ||
            label.includes("主摄") ||
            label.includes("主相机") ||
            label.includes("后摄") ||
            label.includes("背面") ||
            label.includes("后面") ||
            // 日文标签
            label.includes("背面") ||
            label.includes("トリプル") ||
            label.includes("デュアル") ||
            label.includes("超広角") ||
            label.includes("広角") ||
            label.includes("望遠") ||
            label.includes("メイン") ||
            label.includes("プライマリ") ||
            // 韩文标签
            label.includes("후면") ||
            label.includes("뒷면") ||
            label.includes("메인") ||
            label.includes("주") ||
            label.includes("기본") ||
            // 品牌特定标签
            // Samsung
            label.includes("triple") ||
            label.includes("dual") ||
            label.includes("ultra wide") ||
            label.includes("telephoto") ||
            label.includes("zoom") ||
            // Huawei/Honor
            label.includes("leica") ||
            label.includes("periscope") ||
            label.includes("超感知") ||
            label.includes("潜望式") ||
            // Xiaomi
            label.includes("小米") ||
            label.includes("redmi") ||
            label.includes("108mp") ||
            label.includes("50mp") ||
            label.includes("48mp") ||
            // Oppo/OnePlus
            label.includes("hasselblad") ||
            label.includes("portrait") ||
            label.includes("macro") ||
            // Vivo
            label.includes("zeiss") ||
            label.includes("gimbal") ||
            // Apple
            label.includes("cinematic") ||
            label.includes("photographic") ||
            // 通用摄像头类型标识
            label.includes("wide angle") ||
            label.includes("ultra-wide") ||
            label.includes("tele") ||
            label.includes("periscope") ||
            label.includes("macro") ||
            label.includes("depth") ||
            label.includes("monochrome") ||
            label.includes("b&w") ||
            label.includes("black and white") ||
            // 数字标识（通常后置摄像头）
            label.includes("camera 1") ||
            label.includes("camera1") ||
            label.includes("cam 1") ||
            label.includes("cam1") ||
            // 通用视频设备标识 - 通常 Video device 1 是后置摄像头
            label.includes("video device 1") ||
            label.includes("video device1") ||
            label.includes("videodevice 1") ||
            label.includes("videodevice1") ||
            // 其他语言
            // 法语
            label.includes("arrière") ||
            label.includes("principal") ||
            // 德语
            label.includes("haupt") ||
            label.includes("rück") ||
            // 西班牙语
            label.includes("trasera") ||
            label.includes("principal") ||
            // 意大利语
            label.includes("posteriore") ||
            label.includes("principale") ||
            // 俄语
            label.includes("основная") ||
            label.includes("задняя") ||
            // 阿拉伯语相关
            label.includes("خلفية") ||
            label.includes("رئيسية") ||
            // 印地语相关
            label.includes("मुख्य") ||
            label.includes("पीछे")
          );
        });

        if (backCamera) {
          console.log("[QRCode] 找到明确的后置摄像头:", backCamera.label);
          // 设置摄像头索引为找到的后置摄像头
          this.cameraIndex = videoDevices.findIndex(
            (device) => device.deviceId === backCamera.deviceId
          );
          return backCamera.deviceId;
        }

        // 优先级2：排除明确标识为前置摄像头的设备，选择剩余的第一个
        const frontCameraKeywords = [
          // 英文前置摄像头关键词
          "front",
          "user",
          "facing",
          "selfie",
          "face",
          "inner",
          "secondary",

          // 中文前置摄像头关键词
          "前置",
          "自拍",
          "内置",
          "前摄",
          "前面",
          "人脸",

          // 日文前置摄像头关键词
          "前面",
          "フロント",
          "セルフィー",
          "自撮り",
          "インナー",

          // 韩文前置摄像头关键词
          "전면",
          "앞면",
          "셀피",
          "자촬",
          "사용자",

          // 其他语言前置摄像头关键词
          // 法语
          "avant",
          "utilisateur",

          // 德语
          "vorder",
          "benutzer",

          // 西班牙语
          "frontal",
          "usuario",

          // 意大利语
          "anteriore",
          "utente",

          // 俄语
          "передняя",
          "пользователь",

          // 数字标识（通常前置摄像头）
          "camera 2",
          "camera2",
          "cam 2",
          "cam2",
          // 通用视频设备标识 - 通常 Video device 2 是前置摄像头
          "video device 2",
          "video device2",
          "videodevice 2",
          "videodevice2",
        ];

        const nonFrontCameras = videoDevices.filter((device) => {
          const label = device.label.toLowerCase();
          return !frontCameraKeywords.some((keyword) =>
            label.includes(keyword)
          );
        });

        if (nonFrontCameras.length > 0) {
          backCamera = nonFrontCameras[0];
          console.log("[QRCode] 通过排除法找到后置摄像头:", backCamera.label);
          // 设置摄像头索引
          this.cameraIndex = videoDevices.findIndex(
            (device) => device.deviceId === backCamera.deviceId
          );
          return backCamera.deviceId;
        }

        // 优先级3：针对通用"Video device"标签的智能识别
        if (videoDevices.length > 1) {
          const videoDeviceList = videoDevices.filter((device) =>
            device.label.toLowerCase().includes("video device")
          );

          if (videoDeviceList.length >= 2) {
            // 对于通用Video device标签，通常较小的数字是后置摄像头
            const sortedVideoDevices = videoDeviceList.sort((a, b) => {
              const aNum = parseInt(a.label.match(/\d+/)?.[0] || "0");
              const bNum = parseInt(b.label.match(/\d+/)?.[0] || "0");
              return aNum - bNum;
            });

            backCamera = sortedVideoDevices[0]; // 选择数字最小的
            console.log(
              "[QRCode] 通过Video device数字排序选择后置摄像头:",
              backCamera.label
            );
            // 设置摄像头索引
            this.cameraIndex = videoDevices.findIndex(
              (device) => device.deviceId === backCamera.deviceId
            );
            return backCamera.deviceId;
          }

          // 如果不是Video device标签，按原逻辑选择第二个
          backCamera = videoDevices[1];
          console.log("[QRCode] 通过索引选择后置摄像头:", backCamera.label);
          // 设置摄像头索引
          this.cameraIndex = 1;
          return backCamera.deviceId;
        }

        // 最后：返回第一个可用设备
        console.log("[QRCode] 使用第一个可用设备:", videoDevices[0].label);
        // 设置摄像头索引
        this.cameraIndex = 0;
        return videoDevices[0].deviceId;
      } catch (error) {
        console.error("[QRCode] 获取摄像头设备失败:", error);
        return null;
      }
    },

    // 强制释放所有摄像头资源
    async forceReleaseAllCameraResources() {
      console.log("[QRCode] forceReleaseAllCameraResources called");

      // 强制释放所有摄像头资源
      this.releaseVideoResources();

      // 重置摄像头索引
      this.cameraIndex = 0;
      this.availableCameras = [];

      // 等待一段时间确保资源完全释放
      await new Promise((resolve) => setTimeout(resolve, 500));

      // 尝试强制垃圾回收
      if (window.gc && typeof window.gc === "function") {
        try {
          window.gc();
          console.log("[QRCode] 已触发强制垃圾回收");
        } catch (error) {
          console.log("[QRCode] 强制垃圾回收不可用");
        }
      }

      console.log("[QRCode] 已强制释放所有摄像头资源");
    },
  },
};
</script>
<style lang="scss" scoped>
.container {
  position: relative;
  width: 100vw;
  height: 100vh;
  background-color: #000;
  overflow: hidden;
}

.scan-box {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.scan-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  pointer-events: none;
  -webkit-transform: none !important;
  transform: none !important;
}

::v-deep .uni-video-video {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
}

::v-deep .uni-video-cover,
.uni-video-control {
  display: none !important;
}

.scan-canvas {
  display: none;
}

.scan-line {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 240px;
  height: 240px;
  transform: translate(-50%, -50%);
  border: 1px solid #333;
  box-shadow: 0 0 0 4000px rgba(0, 0, 0, 0.6);
}

.scan-border {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 240px;
  height: 240px;
  transform: translate(-50%, -50%);
  border: 5px solid #138071;
  animation: scanBorder 2s linear infinite;
}

.tip-text {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 100px;
  color: #ffffff;
  text-align: center;
  font-size: 16px;

  .barcode-tip {
    margin-top: 10px;
    font-size: 14px;
    color: #ffcc00; // 黄色提示更醒目
    font-weight: bold;
    animation: pulse 2s infinite; // 添加脉冲动画提高注意力
  }

  .qrcode-tip {
    margin-top: 10px;
    font-size: 14px;
    color: #00ccff; // 蓝色提示区分二维码模式
    font-weight: bold;
    animation: pulse 2s infinite;
  }

  .mode-restriction {
    display: inline-block;
    margin-top: 5px;
    padding: 3px 8px;
    background-color: rgba(0, 0, 0, 0.6);
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
  }
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

.success-effect {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease;
  z-index: 999;
}

.success-icon {
  width: 100px;
  height: 100px;
  background: #138071;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: scaleIn 0.3s ease;
}

.success-text {
  color: #fff;
  font-size: 18px;
  margin-top: 20px;
}

@keyframes scanBorder {
  0% {
    clip-path: inset(0 98% 98% 0);
  }
  25% {
    clip-path: inset(0 0 98% 0);
  }
  50% {
    clip-path: inset(0 0 0 98%);
  }
  75% {
    clip-path: inset(98% 0 0 0);
  }
  100% {
    clip-path: inset(0 98% 98% 0);
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.5);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

:deep(.uni-video-video) {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
}

::v-deep .uni-video-cover,
::v-deep .uni-video-control,
::v-deep .uni-video-poster,
::v-deep .uni-video-button,
::v-deep .uni-video-fullscreen,
::v-deep .uni-video-center-button,
::v-deep .uni-video-seekbar,
::v-deep .uni-video-seekbar-wrap,
::v-deep .uni-video-seekbar-thumb,
::v-deep .uni-video-seekbar-progress,
::v-deep .uni-video-seekbar-buffer,
::v-deep .uni-video-seekbar-played {
  display: none !important;
}

.camera-toggle-btn {
  position: absolute;
  z-index: 1000;
  top: 30px;
  right: 20px;
  padding: 10px 15px;
  background-color: rgba(255, 255, 255, 0.3);
  color: #fff;
  border-radius: 8px;
  text-align: center;
}

.flashlight-btn {
  position: absolute;
  z-index: 1000;
  bottom: 180px;
  left: 50%;
  transform: translateX(-50%);
  padding: 10px 20px;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  border-radius: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  text {
    margin-top: 5px;
    font-size: 14px;
  }
}

.scan-mode-container {
  position: absolute;
  z-index: 1000;
  bottom: 240px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 15px; /* 增加按钮间距 */
  padding: 5px;
}

.scan-mode-btn {
  padding: 12px 20px; /* 增加按钮大小 */
  border-radius: 25px; /* 更圆润的边角 */
  background-color: rgba(0, 0, 0, 0.7); /* 更深的背景色 */
  color: #fff;
  font-size: 16px; /* 更大的字体 */
  text-align: center;
  transition: all 0.3s;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3); /* 添加阴影效果 */
  border: 1px solid rgba(255, 255, 255, 0.2); /* 添加边框 */

  &.active {
    background-color: #138071;
    color: #fff;
    font-weight: bold;
    transform: scale(1.05); /* 活跃状态稍微放大 */
    box-shadow: 0 4px 12px rgba(19, 128, 113, 0.5); /* 活跃状态阴影更明显 */
  }

  text {
    font-size: 16px;
  }
}
</style>
