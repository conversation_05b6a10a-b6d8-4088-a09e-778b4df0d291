const fs = require('fs')
const path = require('path')

module.exports = {
    css: {
        loaderOptions: {
            postcss: {
                plugins: [
                    require("tailwindcss"),
                    require("autoprefixer")
                ],
            },
        },
    },
    // 配置webpack
    configureWebpack: config => {
        if (process.env.NODE_ENV === 'production') {
            // 生产环境配置
            config.mode = 'production'
            config.optimization = {
                minimize: true
            }
        } else {
            // 开发环境配置 
            config.mode = 'development'
            config.devtool = 'source-map'
        }
    }
}
