<template>
  <view class="map-container">
    <div id="sgMap" class="map-box"></div>
    <div id="panel" class="result-panel"></div>

    <!-- 控制按钮区 -->
    <div class="map-ctrl-wrap">
      <div class="line-wrap">
        <van-button class="reroute-btn" @click="replanRoute"
          >重新规划路径</van-button
        >
      </div>
    </div>

    <div
      v-if="loading"
      class="w-full h-full text-white fixed top-0 left-0 bg-black opacity-50 z-50 flex justify-center items-center"
    >
      <van-loading type="spinner" color="#fff" size="24px" />
      加载中...
    </div>
  </view>
</template>

<script>
import { Loading } from "vant";
import { isProd } from "@/utils/env";

export default {
  components: {
    "van-loading": Loading,
  },
  data() {
    return {
      loading: true,
      map: null,
      walkingTask: null,
      geocodingPlusTask: null,
      key: "29157aa48d4739d297756afb1395ea81",
      secret: "dcf32339ed5f398ba00358c38cf33e47",
      plugin: [
        "SGMap.GeolocationTask",
        "SGMap.WalkingPlusTask",
        "SGMap.GeocodingPlusTask",
      ],
      endPoint: null,
      agentId: navigator.userAgent.toLowerCase().includes("uat")
        ? "1006802"
        : "1001657",
    };
  },
  onLoad(options) {
    console.log("[地图] onLoad options:", options);
    if (isProd()) {
      console.log("[地图] 生产环境不加载地图组件");
      uni.navigateBack();
      return;
    }

    // 获取路由参数
    this.address = options.address;
    this.cityName = options.cityName;

    // 验证必要参数
    if (!this.address || !this.cityName) {
      console.warn("[地图] 缺少必要参数:", {
        address: this.address,
        cityName: this.cityName,
      });
      uni.showToast({
        title: "地址信息不完整",
        icon: "none",
        duration: 2000,
      });
      setTimeout(() => {
        uni.navigateBack();
      }, 2000);
      return;
    }

    // 加载地图脚本
    const script = document.createElement("script");
    script.src = "https://map.sgcc.com.cn/maps?v=3.0.0";
    document.head.appendChild(script);
    script.onload = () => {
      console.log("[地图] 地图脚本加载成功");
      this.initLogin();
    };
    script.onerror = (error) => {
      console.error("[地图] 地图脚本加载失败:", error);
    };
  },
  methods: {
    async initLogin() {
      console.log("[地图] 开始初始化登录");
      try {
        console.log("[地图] 尝试登录 key:", this.key);
        await SGMap.tokenTask.login(this.key, this.secret);
        console.log("[地图] 登录成功，加载插件:", this.plugin);
        await SGMap.plugin(this.plugin);
        console.log("[地图] 插件加载成功");
        this.geocodingPlusTask = new SGMap.GeocodingPlusTask({
          city: this.cityName || "襄阳", // 使用传入的城市或默认值
          extensions: "base",
        });
        this.initMap();
      } catch (e) {
        console.error("[地图] 初始化失败:", e);
        throw e;
      }
    },
    initMap() {
      console.log("[地图] 开始初始化地图");
      this.map = new SGMap.Map({
        container: "sgMap",
        style: "aegis://styles/aegis/Streets-v2",
        zoom: 11,
        center: [116.306007, 39.979771],
        localIdeographFontFamily: "Microsoft YaHei",
      });

      this.map.on("load", async () => {
        console.log("[地图] 地图加载完成，获取当前位置");
        try {
          const data = await new SGMap.GeolocationTask().getLocation();
          console.log("[地图] 当前位置:", data[0]);
          this.inited(data[0]);
        } catch (e) {
          console.error("[地图] 获取位置失败:", e);
        }
      });
    },
    inited(startPoint) {
      console.log("[地图] 初始化导航任务, startPoint:", startPoint);
      this.walkingTask = new SGMap.WalkingPlusTask({
        map: this.map,
        panel: "panel", // 添加结果面板
      });
      this.search(startPoint);
    },
    async search(startPoint) {
      console.log("[地图] 开始搜索路径, startPoint:", startPoint);
      try {
        // 添加参数验证
        if (!this.address || !this.cityName) {
          console.error("[地图] 终点地址或城市为空:", {
            address: this.address,
            cityName: this.cityName,
          });
          uni.showToast({
            title: "终点地址不能为空",
            icon: "none",
            duration: 2000,
          });
          this.loading = false;
          return;
        }

        const { regeocodes } = await this.geocodingPlusTask.getAddress(
          startPoint
        );
        console.log("[地图] 地理编码结果:", regeocodes);

        await new Promise((resolve, reject) => {
          console.log("[地图] 开始路径规划:", {
            起点地址: regeocodes[0].formatted_address,
            起点城市: regeocodes[0].addressComponent.city,
            终点地址: this.address,
            终点城市: this.cityName,
          });

          // 确保地址不为空
          if (!regeocodes[0].formatted_address || !this.address) {
            reject(new Error("起点或终点地址为空"));
            return;
          }

          this.walkingTask.search({
            points: [
              {
                keyword: regeocodes[0].formatted_address,
                city: regeocodes[0].addressComponent.city,
                location: startPoint.join(","), // 使用字符串格式的坐标
              },
              {
                keyword: this.address,
                city: this.cityName,
              },
            ],
            complete: (status, result) => {
              console.log("[地图] 路径规划状态:", status);
              console.log("[地图] 路径规划结果:", result);

              if (result?.status === "1" && result?.info === "ok") {
                try {
                  // 修改：从route.destination获取终点坐标
                  if (!result.route?.destination) {
                    console.log(
                      "[地图] 尝试从route.destination获取坐标失败，尝试其他方式"
                    );
                    // 尝试从paths中获取最后一个点
                    if (result.route?.paths?.[0]?.steps?.length > 0) {
                      const lastStep =
                        result.route.paths[0].steps[
                          result.route.paths[0].steps.length - 1
                        ];
                      if (lastStep.end_location) {
                        this.endPoint = {
                          lng: Number(lastStep.end_location.split(",")[0]),
                          lat: Number(lastStep.end_location.split(",")[1]),
                        };
                        console.log(
                          "[地图] 从steps中获取到终点坐标:",
                          this.endPoint
                        );
                        resolve(result);
                        return;
                      }
                    }
                    throw new Error("未找到目标坐标");
                  }

                  const [lng, lat] = result.route.destination
                    .split(",")
                    .map(Number);

                  if (!isNaN(lng) && !isNaN(lat)) {
                    this.endPoint = { lng, lat };
                    console.log("[地图] 成功解析终点坐标:", this.endPoint);
                    resolve(result);
                  } else {
                    throw new Error("坐标解析失败");
                  }
                } catch (err) {
                  console.error("[地图] 坐标处理错误:", err);
                  reject(err);
                }
              } else {
                const errorMsg = result?.info || "路径规划失败";
                console.error("[地图] 路径规划失败:", errorMsg);
                reject(new Error(errorMsg));
              }
            },
          });
        });

        this.loading = false;
      } catch (e) {
        console.error("[地图] 搜索过程错误:", e);
        uni.showToast({
          title: e.message || "获取路径失败",
          icon: "none",
          duration: 2000,
        });
        this.loading = false;
      }
    },
    async replanRoute() {
      console.log("[地图] 开始重新规划路径");
      this.loading = true;
      try {
        const data = await new SGMap.GeolocationTask().getLocation();
        console.log("[地图] 重新获取当前位置:", data[0]);
        await this.search(data[0]);
      } catch (e) {
        console.error("[地图] 重新规划路径失败:", e);
        uni.showToast({
          title: "重新规划失败",
          icon: "none",
        });
      }
      this.loading = false;
    },
    openMapApp(address, latitude, longitude) {
      console.log("[地图] 打开地图应用:", {
        address,
        latitude,
        longitude,
        agentId: this.agentId,
      });
      const url = encodeURI(
        `zipapp://appid.${this.agentId}/index.html?scheme=route&dname=${address}&dlat=${latitude}&dlon=${longitude}&closeWithEndNavi=1`
      );

      wx.invoke(
        "multiWindows_startWidget",
        {
          agentId: this.agentId,
          window: {
            windowId: "sgmap_app",
            url: url,
            openType: 1,
            showAppBar: "false",
          },
        },
        (res) => {
          console.log("打开思极地图微应用 res = ", res);
          uni.navigateBack();
        }
      );
    },
  },
};
</script>

<style lang="scss">
.map-container {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;

  .map-box {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
  }

  .result-panel {
    position: fixed;
    background-color: white;
    max-height: 80vh;
    top: calc(44px + 10px); // 导航栏高度 + 间距
    right: 10px;
    width: 280px;
    z-index: 100;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    overflow-y: auto;
  }

  .map-ctrl-wrap {
    position: fixed;
    bottom: calc(env(safe-area-inset-bottom, 20px) + 20px);
    left: 50%;
    transform: translateX(-50%);
    z-index: 100;

    .line-wrap {
      display: flex;
      justify-content: center;
      padding: 10px;

      .reroute-btn {
        background: #4caf50;
        color: white;
        border-radius: 4px;
        padding: 0 20px;
      }
    }
  }
}

// 隐藏uni-app默认导航栏
::v-deep {
  .uni-page-head {
    background: transparent !important;
  }
  .uni-page-head-bd {
    opacity: 0;
  }
}
</style>
