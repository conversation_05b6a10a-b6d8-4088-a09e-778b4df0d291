<template>
  <div class="page-container">
    <van-skeleton fullscreen :loading="loading">
      <!-- 修改为循环渲染上传组件 -->
      <template v-if="uploadConfigs.length > 0">
        <van-cell-group
          v-for="config in uploadConfigs"
          :key="config.id"
          class="upload-section"
        >
          <van-cell :title="config.label" :required="config.required" />
          <div class="image-uploader p-4">
            <uni-file-picker
              v-model="imageListMap[config.id]"
              :auto-upload="false"
              fileMediatype="image"
              mode="grid"
              :limit="config.limit"
              @select="(e) => handleFileSelect(e, config.id)"
              @delete="(e) => handleDelete(e, config.id)"
              :sourceType="['album']"
            />
          </div>
        </van-cell-group>
      </template>

      <!-- formType 5：工单走访 6：工单巡视时显示95598工单 -->
      <div v-if="formType == '5' || formType == '6'" class="content-wrapper">
        <van-tabs v-model="active">
          <van-tab title="填写表单" name="1">
            <div class="form-content p-2">
              <vm-form-render
                v-if="formReady"
                :form-json="formJson"
                :form-data="formData"
                :globalDsv="globalDsv"
                :option-data="optionData"
                ref="vFormRef"
              />
            </div>
          </van-tab>
          <van-tab title="95598工单" name="2">
            <div class="form-content p-2">
              <van-form>
                <van-cell-group inset>
                  <van-field
                    v-for="item in workOrderList"
                    :key="item.label"
                    v-model="item.value"
                    :label="item.label"
                    :placeholder="item.label"
                    label-width="90px"
                    readonly
                    colon
                  />
                </van-cell-group>
              </van-form>
            </div>
          </van-tab>
        </van-tabs>
      </div>
      <div v-else class="content-wrapper box-border">
        <div class="form-content p-2">
          <vm-form-render
            v-if="formReady"
            :form-json="formJson"
            :form-data="formData"
            :globalDsv="globalDsv"
            :option-data="optionData"
            ref="vFormRef"
          />
          <van-cell-group
            v-if="codeExceptionType === '3'"
            class="exception-input"
          >
            <van-field
              v-model="exceptionInfo"
              label="异常描述"
              placeholder="请输入异常描述"
              type="textarea"
            />
          </van-cell-group>
        </div>
      </div>
    </van-skeleton>

    <!-- 修改提交按钮的渲染逻辑 -->
    <div class="submit-block" v-if="formJson && !loading">
      <div v-if="locationError" class="location-error">
        {{ locationError }}
      </div>
      <van-button
        size="large"
        block
        type="primary"
        class="submit-button"
        @click="onSubmit"
      >
        <div class="button-content">
          <span class="submit-text">提交</span>
          <div class="geofence-status" v-if="!locationError">
            <span
              :class="{
                'geofence-inside': isInsideGeofence,
                'geofence-outside': !isInsideGeofence,
              }"
            >
              {{ isInsideGeofence ? "在指定区域内" : "不在指定区域内" }}
            </span>
            <van-icon
              :name="isInsideGeofence ? 'success' : 'cross'"
              :class="{
                'text-success': isInsideGeofence,
                'text-danger': !isInsideGeofence,
              }"
              size="14"
            />
          </div>
        </div>
      </van-button>
    </div>
  </div>
</template>

<style>
.page-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow-y: auto;
  padding: 16px;
  padding-bottom: 100px;
  overflow-x: hidden;
  box-sizing: border-box;
}

.exception-input {
  margin: 16px 10px;
}

.submit-block {
  padding: 0 10px;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  background: #fff;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
  z-index: 999;
}

.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.submit-text {
  font-size: 16px;
}

.geofence-status {
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.geofence-inside {
  color: #e8f5e9;
}

.geofence-outside {
  color: #ffebee;
}

.text-success {
  color: #e8f5e9;
}

.text-danger {
  color: #ffebee;
}

.location-error {
  margin-bottom: 8px;
  padding: 6px 8px;
  font-size: 11px;
  color: #999;
  background: #f8f9fa;
  border-radius: 4px;
  text-align: center;
  opacity: 0.8;
}

.upload-section {
  background: #fff;
  border-radius: 8px;
  margin: 16px 0;
}

.image-uploader {
  background: #fff;
}

.upload-tip {
  color: #999;
  font-size: 12px;
}
</style>

<script>
import { submitForm, revokeForm, uploadImage } from "@/api/index";
import { getFormDetail, getDefaultForm } from "@/api";
import { getToken } from "@/utils/auth";
import { writeLog, LogLevel } from "@/utils/logger";

export default {
  data() {
    return {
      loading: true,
      active: "1",
      formData: {},
      globalDsv: {
        fileId: "",
      },
      exceptionInfo: null,
      workOrderList: [],
      formJson: null,
      optionData: null,
      formType: "",
      codeExceptionType: "",
      formDataId: "",
      formId: "",
      towerId: "",
      tacticsDetailsInfoVO: {},
      hasError: false,
      scanData: null,
      parsedScanData: null, // 新增解析后的扫码数据
      formReady: false, // 添加表单准备状态标识
      isFromDefaultForm: false, // 新增标记是否来自自助填报
      defaultFormDataId: "", // 新增存储自助填报的formDataId
      imageList: [], // 存储上传的图片列表
      imageUrls: [], // 存储上传成功后的图片URL列表
      hasUploadComponent: false, // 是否存在上传组件
      uploadConfig: {}, // 存储上传组件的配置
      uploadedFiles: [], // 存储已上传文件的信息
      uploadWidgetId: "", // 存储上传组件的ID
      uploadConfigs: [], // 存储所有上传组件的配置
      imageListMap: {}, // 存储每个上传组件的图片列表
      imageUrlsMap: {}, // 存储每个上传组件的图片URL列表
      // 添加电子围栏相关数据
      formLongitude: null,
      formLatitude: null,
      userLongitude: null,
      userLatitude: null,
      isInsideGeofence: false,
      locationError: null,
      geofenceRadius: 1000, // 电子围栏半径（米）
      // 添加思极地图相关配置
      key: "29157aa48d4739d297756afb1395ea81",
      secret: "dcf32339ed5f398ba00358c38cf33e47",
      plugin: ["SGMap.GeolocationTask"],
      mapSDKLoaded: false, // 添加地图SDK加载状态标识
    };
  },
  async onLoad({
    scanData: resScanData,
    formId,
    formDataId,
    codeExceptionType,
  }) {
    if (resScanData) {
      try {
        // 解析传入的JSON字符串
        const decodedData = JSON.parse(decodeURIComponent(resScanData));

        // 如果是扫码数据，进行进一步处理
        if (decodedData.code === 200 && decodedData.data) {
          this.parsedScanData = decodedData.data;
          this.scanData = {
            data: {
              // 移除 scanCodeType，只保留必要字段
              device: decodedData.data.device,
              custom: decodedData.data.custom,
              codeId: decodedData.data.codeId,
            },
          };
        }
      } catch (error) {
        console.error("解析scanData失败:", error);
        uni.showToast({
          title: "数据解析失败",
          icon: "error",
        });
        return;
      }
    } else {
      // 如果是普通表单数据，保持原有逻辑
      this.formId = formId;
      this.formDataId = formDataId;
      this.codeExceptionType = codeExceptionType || "";
    }
    await this.initFormData();
    // 初始化表单数据后获取用户位置
    this.getUserLocation();
  },
  // 添加 onBackPress 生命周期
  onBackPress() {
    if (this.formDataId) {
      // 判断是否是默认表单类型(7:默认走访 8:默认巡视)
      if (this.formType === "7" || this.formType === "8") {
        try {
          // 添加日志
          writeLog(LogLevel.INFO, "form撤销", "准备调用撤销接口", {
            formDataId: this.formDataId,
            formType: this.formType,
          });

          const params = {
            formDataId: this.formDataId,
          };
          revokeForm(params);
          writeLog(LogLevel.INFO, "form撤销", "撤销调用成功");
        } catch (error) {
          writeLog(LogLevel.ERROR, "form撤销", "撤销调用失败", {
            error: error.message,
            formDataId: this.formDataId,
          });
          console.error("撤销失败:", error);
        }
      }
      return false; // 返回false继续执行默认的返回逻辑
    }
  },
  mounted() {
    setTimeout(() => {
      this.loading = false;
    }, 200);
  },
  methods: {
    // 添加地图脚本加载方法
    loadMapSDK() {
      return new Promise((resolve, reject) => {
        if (window.SGMap) {
          this.mapSDKLoaded = true;
          resolve();
          return;
        }

        const script = document.createElement("script");
        script.src = "https://map.sgcc.com.cn/maps?v=3.0.0";
        script.onload = () => {
          console.log("[位置服务] 思极地图脚本加载成功");
          this.mapSDKLoaded = true;
          resolve();
        };
        script.onerror = (error) => {
          console.error("[位置服务] 思极地图脚本加载失败:", error);
          reject(new Error("地图脚本加载失败"));
        };
        document.head.appendChild(script);
      });
    },

    // 添加计算两点距离的方法（Haversine公式）
    calculateDistance(lat1, lon1, lat2, lon2) {
      // 将经纬度转换为弧度
      const toRad = (value) => (value * Math.PI) / 180;

      const R = 6371000; // 地球半径，单位米
      const dLat = toRad(lat2 - lat1);
      const dLon = toRad(lon1 - lon2);

      const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos(toRad(lat1)) *
          Math.cos(toRad(lat2)) *
          Math.sin(dLon / 2) *
          Math.sin(dLon / 2);

      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
      const distance = R * c; // 两点之间的距离，单位为米

      return distance;
    },

    // 修改获取位置方法
    async getUserLocation() {
      writeLog(LogLevel.INFO, "电子围栏检测", "开始获取位置");
      // 先检查表单位置信息是否完整
      if (!this.formLongitude || !this.formLatitude) {
        this.locationError = "表单位置信息不完整，无法判断位置范围";
        writeLog(LogLevel.WARN, "电子围栏检测", "表单位置信息不完整", {
          formLongitude: this.formLongitude,
          formLatitude: this.formLatitude,
        });
        return;
      }

      try {
        // 确保地图SDK已加载
        if (!this.mapSDKLoaded) {
          writeLog(LogLevel.INFO, "电子围栏检测", "地图SDK未加载，开始加载SDK");
          try {
            await this.loadMapSDK();
            writeLog(LogLevel.INFO, "电子围栏检测", "地图SDK加载成功");
          } catch (sdkError) {
            writeLog(LogLevel.ERROR, "电子围栏检测", "地图SDK加载失败", {
              error: sdkError?.message || String(sdkError),
              stack: sdkError?.stack,
            });
            throw new Error(
              `地图SDK加载失败: ${sdkError?.message || String(sdkError)}`
            );
          }
        }

        // 检查SGMap对象是否存在
        if (!window.SGMap) {
          writeLog(
            LogLevel.ERROR,
            "电子围栏检测",
            "SGMap对象不存在，SDK可能未正确加载"
          );
          throw new Error("SGMap对象不存在，SDK可能未正确加载");
        }

        writeLog(LogLevel.INFO, "电子围栏检测", "开始初始化思极地图", {
          key: this.key ? "已设置" : "未设置",
          secret: this.secret ? "已设置" : "未设置",
          plugin: JSON.stringify(this.plugin),
        });

        // 初始化思极地图
        try {
          await SGMap.tokenTask.login(this.key, this.secret);
          writeLog(LogLevel.INFO, "电子围栏检测", "思极地图登录成功");
        } catch (loginError) {
          writeLog(LogLevel.ERROR, "电子围栏检测", "思极地图登录失败", {
            error: loginError?.message || String(loginError),
            stack: loginError?.stack,
          });
          throw new Error(
            `思极地图登录失败: ${loginError?.message || String(loginError)}`
          );
        }

        try {
          await SGMap.plugin(this.plugin);
          writeLog(LogLevel.INFO, "电子围栏检测", "思极地图插件加载成功");
        } catch (pluginError) {
          writeLog(LogLevel.ERROR, "电子围栏检测", "思极地图插件加载失败", {
            error: pluginError?.message || String(pluginError),
            stack: pluginError?.stack,
          });
          throw new Error(
            `思极地图插件加载失败: ${
              pluginError?.message || String(pluginError)
            }`
          );
        }

        // 使用思极地图获取位置
        writeLog(LogLevel.INFO, "电子围栏检测", "开始获取位置信息");
        let data;
        try {
          const geoTask = new SGMap.GeolocationTask();
          writeLog(
            LogLevel.INFO,
            "电子围栏检测",
            "GeolocationTask实例创建成功"
          );
          data = await geoTask.getLocation();
          writeLog(LogLevel.INFO, "电子围栏检测", "位置信息获取成功", {
            dataExists: !!data,
            dataLength: data ? data.length : 0,
            dataFirstItem: data && data[0] ? "存在" : "不存在",
          });
        } catch (geoError) {
          writeLog(LogLevel.ERROR, "电子围栏检测", "位置信息获取失败", {
            error: geoError?.message || String(geoError),
            stack: geoError?.stack,
          });
          throw new Error(
            `位置信息获取失败: ${geoError?.message || String(geoError)}`
          );
        }

        if (!data || !data[0]) {
          writeLog(LogLevel.ERROR, "电子围栏检测", "获取到的位置数据为空");
          throw new Error("获取位置信息失败，返回数据为空");
        }

        // 获取到的位置格式为 [longitude, latitude]
        writeLog(LogLevel.INFO, "电子围栏检测", "解析位置数据", {
          rawData: JSON.stringify(data[0]),
        });
        this.userLongitude = data[0][0];
        this.userLatitude = data[0][1];

        writeLog(LogLevel.INFO, "电子围栏检测", "开始计算距离");
        // 计算用户与表单中心点的距离
        const distance = this.calculateDistance(
          this.formLatitude,
          this.formLongitude,
          this.userLatitude,
          this.userLongitude
        );

        // 判断是否在电子围栏内
        this.isInsideGeofence = distance <= this.geofenceRadius;

        // 记录日志
        writeLog(LogLevel.INFO, "电子围栏检测", "位置检测结果", {
          formLat: this.formLatitude,
          formLng: this.formLongitude,
          userLat: this.userLatitude,
          userLng: this.userLongitude,
          distance: distance,
          inGeofence: this.isInsideGeofence,
          geofenceRadius: this.geofenceRadius,
        });
      } catch (err) {
        // 捕获任何可能的异常
        console.error("位置获取过程出现异常:", err);
        this.locationError = "获取位置时发生错误，请稍后重试";
        this.isInsideGeofence = false;

        writeLog(LogLevel.ERROR, "电子围栏检测", "位置获取异常", {
          error: err?.message || String(err),
          stack: err?.stack,
          errorType: err?.constructor?.name || typeof err,
        });

        // 检查是否是网络错误
        if (
          err.message &&
          (err.message.includes("network") ||
            err.message.includes("网络") ||
            err.message.includes("timeout") ||
            err.message.includes("超时"))
        ) {
          writeLog(LogLevel.ERROR, "电子围栏检测", "疑似网络问题", {
            message: err.message,
          });
        }

        // 检查是否是权限错误
        if (
          err.message &&
          (err.message.includes("permission") ||
            err.message.includes("权限") ||
            err.message.includes("denied") ||
            err.message.includes("拒绝"))
        ) {
          writeLog(LogLevel.ERROR, "电子围栏检测", "疑似权限问题", {
            message: err.message,
          });
        }
      }
    },

    // 添加深拷贝过滤方法
    filterUploadWidget(widgetList) {
      // 先进行深拷贝
      const deepCopy = JSON.parse(JSON.stringify(widgetList));
      // 再过滤上传组件
      return deepCopy.filter((widget) => widget.type !== "m-picture-upload");
    },

    // 检查并提取上传组件配置
    extractUploadConfig(widgetList) {
      // 找到所有上传组件
      const uploadWidgets = widgetList.filter(
        (widget) =>
          widget.type === "m-picture-upload" && widget.options?.uploadURL
      );

      // 初始化每个上传组件的配置和数据结构
      this.uploadConfigs = uploadWidgets.map((widget) => ({
        id: widget.id,
        label: widget.options.label || "图片上传",
        required: !!widget.options.required,
        limit: parseInt(widget.options.limit || 3),
        fileTypes: widget.options.fileTypes || ["jpg", "jpeg", "png"],
        fileMaxSize: parseInt(widget.options.fileMaxSize || 5),
        uploadTip: widget.options.uploadTip || "",
      }));

      // 初始化图片列表映射
      this.uploadConfigs.forEach((config) => {
        this.imageListMap[config.id] = [];
        this.imageUrlsMap[config.id] = [];
      });

      // 深拷贝并过滤上传组件
      return JSON.parse(
        JSON.stringify(
          widgetList.filter((widget) => widget.type !== "m-picture-upload")
        )
      );
    },

    // 添加方法处理文件类型
    getFileTypes() {
      if (
        this.uploadConfig.fileTypes &&
        Array.isArray(this.uploadConfig.fileTypes)
      ) {
        return this.uploadConfig.fileTypes.join(",");
      }
      return "jpg,jpeg,png";
    },

    // 添加获取上传请求头方法
    getUploadHeaders() {
      return {
        Authorization: "Bearer " + getToken(),
      };
    },

    async initFormData() {
      try {
        let data;
        if (this.scanData) {
          // 移除设备/客户码类型判断，直接使用 codeId
          const codeId = this.scanData.data.codeId;
          const res = await getDefaultForm({ codeId });

          data = res.data;
          // 标记是自助填报并保存formDataId
          this.isFromDefaultForm = true;
          this.defaultFormDataId = data.formDataId;
        } else {
          const res = await getFormDetail(this.formId, this.formDataId);
          data = res.data;
        }

        if (!data) {
          throw new Error("获取表单数据为空");
        }

        // 保存表单的经纬度数据
        if (data.longitude && data.latitude) {
          this.formLongitude = parseFloat(data.longitude);
          this.formLatitude = parseFloat(data.latitude);
          writeLog(LogLevel.INFO, "form获取位置", "成功获取表单位置信息", {
            longitude: this.formLongitude,
            latitude: this.formLatitude,
          });
        } else {
          this.locationError = "表单没有提供有效的位置信息";
          writeLog(LogLevel.WARN, "form获取位置", "表单未提供位置信息");
        }

        const {
          widgetList,
          optionData,
          formConfig,
          tacticsDetailsInfoVO,
          formType,
        } = data;

        this.formType = formType;
        this.tacticsDetailsInfoVO = tacticsDetailsInfoVO;
        this.towerId = data.towerId;

        // 解析数据前先进行数据检查
        if (!formConfig || !widgetList) {
          throw new Error("表单配置数据不完整");
        }

        try {
          // 更安全的 JSON 解析
          const parsedFormConfig = JSON.parse(formConfig);
          // 使用深拷贝过滤上传组件
          const rawWidgetList = JSON.parse(widgetList);
          // 使用新的方法处理组件列表
          const parsedWidgetList = this.extractUploadConfig(rawWidgetList);
          console.log("过滤后的组件列表:", parsedWidgetList);

          const parsedOptionData = optionData ? JSON.parse(optionData) : [];

          // 设置表单 JSON 数据
          this.formJson = {
            formConfig: parsedFormConfig,
            widgetList: parsedWidgetList,
          };

          // 转换选项数据
          this.optionData = parsedOptionData.reduce((acc, curr) => {
            if (curr.options && curr.options.name) {
              acc[curr.options.name] = curr.options.optionItems || [];
            }
            return acc;
          }, {});

          // 所有数据准备就绪后，设置 formReady 为 true
          this.$nextTick(() => {
            this.formReady = true;
          });
        } catch (parseError) {
          console.error("解析表单数据失败:", parseError);
          throw new Error("表单数据格式错误");
        }

        this.getworkOrderList();
      } catch (error) {
        console.error("获取表单详情失败:", error);
        this.hasError = true;
        this.formReady = false; // 确保错误时不显示表单

        // 优先使用服务器返回的msg字段
        let errorMsg = "获取表单详情失败";
        if (error.response?.msg) {
          errorMsg = error.response.msg;
        } else if (error.response?.data?.msg) {
          errorMsg = error.response.data.msg;
        } else if (error.message) {
          errorMsg = error.message;
        }

        uni.showModal({
          title: "提示",
          content: errorMsg,
          showCancel: false,
          success: () => {
            // 如果是首次加载失败，返回上一页
            if (this.hasError) {
              uni.navigateBack();
            }
          },
        });

        throw error;
      }
    },
    processObject(inputObj) {
      const outputObj = {};
      for (const key in inputObj) {
        if (inputObj.hasOwnProperty(key)) {
          const value = inputObj[key];
          if (
            Array.isArray(value) &&
            value.some((item) => item.msg && item.msg.includes("文件"))
          ) {
            outputObj[key] = value.map((item) => item.data).join(",");
          } else {
            outputObj[key] = value;
          }
        }
      }
      return outputObj;
    },
    getworkOrderList() {
      if (this.formType == "5" || this.formType == "6") {
        this.workOrderList = [
          { value: this.tacticsDetailsInfoVO.serreqId, label: "服务请求编号" },
          { value: this.tacticsDetailsInfoVO.customName, label: "客户姓名" },
          { value: this.tacticsDetailsInfoVO.customId, label: "客户编号" },
          { value: this.tacticsDetailsInfoVO.empNo, label: "受理人工号" },
          { value: this.tacticsDetailsInfoVO.cityCode, label: "所属城市" },
          { value: this.tacticsDetailsInfoVO.countyCode, label: "所属区县" },
          { value: this.tacticsDetailsInfoVO.timeLimit, label: "办理时限" },
          { value: this.tacticsDetailsInfoVO.replyTel, label: "回复电话" },
          { value: this.tacticsDetailsInfoVO.sendData, label: "派发时间" },
          { value: this.tacticsDetailsInfoVO.acceptContent, label: "受理内容" },
          { value: this.tacticsDetailsInfoVO.handleTime, label: "受理时间" },
        ];
      }
    },
    // 图片上传相关方法
    fileToBase64(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => {
          const base64 = reader.result.split(",")[1];
          resolve(base64);
        };
        reader.onerror = (error) => reject(error);
      });
    },

    async handleFileSelect(e, uploadId) {
      writeLog(LogLevel.INFO, "form文件上传", "开始上传文件", {
        uploadId,
        fileCount: e.tempFiles?.length,
      });
      console.log("选择文件：", e, "组件ID：", uploadId);
      const { tempFiles } = e;
      if (!tempFiles) return;

      const config = this.uploadConfigs.find((c) => c.id === uploadId);
      if (!config) return;

      // 检查文件大小
      const maxSize = (config.fileMaxSize || 5) * 1024 * 1024;
      const isValid = tempFiles.every((file) => {
        if (file.size > maxSize) {
          uni.showToast({
            title: `文件大小不能超过${config.fileMaxSize}MB`,
            icon: "none",
          });
          return false;
        }
        return true;
      });

      if (!isValid) return;

      // 循环上传文件
      for (let i = 0; i < tempFiles.length; i++) {
        try {
          uni.showLoading({
            title: "上传中...",
            mask: true,
          });

          // 读取文件内容
          const base64Data = await new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => {
              resolve(e.target.result); // 保留完整的 base64 字符串
            };
            reader.onerror = () => reject(new Error("读取文件失败"));
            reader.readAsDataURL(tempFiles[i].file);
          });

          if (!base64Data) {
            throw new Error("读取文件失败");
          }

          const res = await uploadImage({
            base64Data,
          });

          writeLog(LogLevel.INFO, "form文件上传", "文件上传成功", {
            uploadId,
            url: res.url,
          });
          // 使用新的返回格式，直接使用 res.url
          const fileInfo = {
            name: tempFiles[i].name || `file_${Date.now()}`,
            url: res.url,
          };

          // 更新对应组件的图片列表
          this.imageUrlsMap[uploadId] = [
            ...(this.imageUrlsMap[uploadId] || []),
            fileInfo,
          ];
          this.imageListMap[uploadId] = [
            ...(this.imageListMap[uploadId] || []),
            {
              ...fileInfo,
              ...tempFiles[i],
            },
          ];
        } catch (error) {
          writeLog(LogLevel.ERROR, "form文件上传", "文件上传异常", {
            uploadId,
            error: error.message,
          });
          console.error("上传失败：", error);

          // 优先使用服务器返回的msg字段
          let errorMsg = "上传失败";
          if (error.response?.msg) {
            errorMsg = error.response.msg;
          } else if (error.message) {
            errorMsg = error.message;
          }

          uni.showToast({
            title: errorMsg,
            icon: "none",
          });
        } finally {
          uni.hideLoading();
        }
      }
    },

    handleDelete(e, uploadId) {
      const { index } = e;
      this.imageUrlsMap[uploadId]?.splice(index, 1);
      this.imageListMap[uploadId]?.splice(index, 1);
    },

    handleUploadError(message) {
      uni.showToast({
        title: message,
        icon: "none",
      });
    },

    async onSubmit() {
      try {
        const formData = await this.$refs.vFormRef.getFormData();

        // 添加所有上传组件的图片数据
        if (
          Array.isArray(this.uploadConfigs) &&
          this.uploadConfigs.length > 0
        ) {
          this.uploadConfigs.forEach((config) => {
            if (config && config.id && this.imageUrlsMap) {
              const imageUrls = this.imageUrlsMap[config.id];
              if (Array.isArray(imageUrls) && imageUrls.length > 0) {
                formData[config.id] = imageUrls;
              }
            }
          });
        }

        const formDataJson = JSON.stringify(formData);

        // 基础参数
        const params = {
          formDataId: this.formDataId,
          formDataJson: formDataJson,
          formId: this.formId,
          towerId: this.towerId,
          isOnSite: this.isInsideGeofence ? "1" : "0", // 添加是否在现场参数
        };

        // 只有当codeExceptionType有有效值时才添加到参数中
        if (this.codeExceptionType && this.codeExceptionType.trim() !== "") {
          params.codeExceptionType = this.codeExceptionType;
        }

        // 只有当exceptionInfo有值时才添加到参数中
        if (this.exceptionInfo && this.exceptionInfo.trim() !== "") {
          params.exceptionInfo = this.exceptionInfo;
        }

        // 记录提交日志，包含位置信息
        writeLog(LogLevel.INFO, "form提交", "提交表单", {
          formId: this.formId,
          isOnSite: params.isOnSite,
          geofenceStatus: this.isInsideGeofence,
          hasCodeExceptionType: !!params.codeExceptionType,
        });

        const res = await submitForm(params);

        writeLog(LogLevel.INFO, "form提交", "提交表单成功", res);
        // 1. 显示成功提示
        uni.showToast({
          title: "提交成功",
          icon: "success",
          duration: 2000,
        });

        // 2. 等待提示显示完毕后跳转到首页
        setTimeout(() => {
          uni.reLaunch({
            url: "/pages/index",
          });
        }, 2000);
      } catch (error) {
        writeLog(LogLevel.ERROR, "form提交", "提交表单失败", {
          error: error.message,
        });
        console.error("Submit form error:", error);

        // 优先使用服务器返回的msg字段
        let errorMsg = "提交失败,请检查填写信息";
        if (error.response?.msg) {
          errorMsg = error.response.msg;
        } else if (error.message) {
          errorMsg = error.message;
        }

        uni.showToast({
          title: errorMsg,
          icon: "none",
          duration: 2000,
        });
      }
    },
  },
};
</script>
